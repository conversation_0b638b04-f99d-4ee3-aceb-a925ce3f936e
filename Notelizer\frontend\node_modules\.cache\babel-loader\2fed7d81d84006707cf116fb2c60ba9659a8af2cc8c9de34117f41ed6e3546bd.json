{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\Notelizer\\\\frontend\\\\src\\\\pages\\\\UserSignup.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UserSignup = () => {\n  _s();\n  const [name, setName] = useState('');\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [message, setMessage] = useState('');\n  const [loading, setLoading] = useState(false);\n  const navigate = useNavigate();\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setMessage('');\n    setLoading(true);\n    try {\n      const res = await fetch('http://localhost:5001/auth/signup', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          name,\n          email,\n          password,\n          role: 'user'\n        })\n      });\n      const data = await res.json();\n      if (res.ok) {\n        setMessage('Signup successful! Redirecting to login...');\n        // Redirect to login page after successful signup\n        setTimeout(() => {\n          navigate('/user/login');\n        }, 1500);\n      } else {\n        setMessage(data.message || 'Signup failed');\n      }\n    } catch (error) {\n      setMessage('Network error. Please try again.');\n      console.error('Signup error:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"form-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"User Signup\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        placeholder: \"Name\",\n        value: name,\n        onChange: e => setName(e.target.value),\n        required: true,\n        disabled: loading\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"email\",\n        placeholder: \"Email\",\n        value: email,\n        onChange: e => setEmail(e.target.value),\n        required: true,\n        disabled: loading\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"password\",\n        placeholder: \"Password\",\n        value: password,\n        onChange: e => setPassword(e.target.value),\n        required: true,\n        disabled: loading\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        disabled: loading,\n        children: loading ? 'Creating Account...' : 'Signup'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this), message && /*#__PURE__*/_jsxDEV(\"p\", {\n      className: message.includes('successful') ? 'success-message' : 'error-message',\n      children: message\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 19\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 44,\n    columnNumber: 5\n  }, this);\n};\n_s(UserSignup, \"suaOVngQ5awiHGrXbwUOUkQFcOo=\", false, function () {\n  return [useNavigate];\n});\n_c = UserSignup;\nexport default UserSignup;\nvar _c;\n$RefreshReg$(_c, \"UserSignup\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "jsxDEV", "_jsxDEV", "UserSignup", "_s", "name", "setName", "email", "setEmail", "password", "setPassword", "message", "setMessage", "loading", "setLoading", "navigate", "handleSubmit", "e", "preventDefault", "res", "fetch", "method", "headers", "body", "JSON", "stringify", "role", "data", "json", "ok", "setTimeout", "error", "console", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "type", "placeholder", "value", "onChange", "target", "required", "disabled", "includes", "_c", "$RefreshReg$"], "sources": ["D:/Projects/Notelizer/frontend/src/pages/UserSignup.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\n\nconst UserSignup = () => {\n  const [name, setName] = useState('');\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [message, setMessage] = useState('');\n  const [loading, setLoading] = useState(false);\n  const navigate = useNavigate();\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setMessage('');\n    setLoading(true);\n\n    try {\n      const res = await fetch('http://localhost:5001/auth/signup', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ name, email, password, role: 'user' })\n      });\n\n      const data = await res.json();\n\n      if (res.ok) {\n        setMessage('Signup successful! Redirecting to login...');\n        // Redirect to login page after successful signup\n        setTimeout(() => {\n          navigate('/user/login');\n        }, 1500);\n      } else {\n        setMessage(data.message || 'Signup failed');\n      }\n    } catch (error) {\n      setMessage('Network error. Please try again.');\n      console.error('Signup error:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"form-container\">\n      <h2>User Signup</h2>\n      <form onSubmit={handleSubmit}>\n        <input\n          type=\"text\"\n          placeholder=\"Name\"\n          value={name}\n          onChange={e => setName(e.target.value)}\n          required\n          disabled={loading}\n        />\n        <input\n          type=\"email\"\n          placeholder=\"Email\"\n          value={email}\n          onChange={e => setEmail(e.target.value)}\n          required\n          disabled={loading}\n        />\n        <input\n          type=\"password\"\n          placeholder=\"Password\"\n          value={password}\n          onChange={e => setPassword(e.target.value)}\n          required\n          disabled={loading}\n        />\n        <button type=\"submit\" disabled={loading}>\n          {loading ? 'Creating Account...' : 'Signup'}\n        </button>\n      </form>\n      {message && <p className={message.includes('successful') ? 'success-message' : 'error-message'}>{message}</p>}\n    </div>\n  );\n};\n\nexport default UserSignup;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGP,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACQ,KAAK,EAAEC,QAAQ,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACU,QAAQ,EAAEC,WAAW,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACY,OAAO,EAAEC,UAAU,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAMgB,QAAQ,GAAGf,WAAW,CAAC,CAAC;EAE9B,MAAMgB,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBN,UAAU,CAAC,EAAE,CAAC;IACdE,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF,MAAMK,GAAG,GAAG,MAAMC,KAAK,CAAC,mCAAmC,EAAE;QAC3DC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UAAE,cAAc,EAAE;QAAmB,CAAC;QAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UAAEpB,IAAI;UAAEE,KAAK;UAAEE,QAAQ;UAAEiB,IAAI,EAAE;QAAO,CAAC;MAC9D,CAAC,CAAC;MAEF,MAAMC,IAAI,GAAG,MAAMR,GAAG,CAACS,IAAI,CAAC,CAAC;MAE7B,IAAIT,GAAG,CAACU,EAAE,EAAE;QACVjB,UAAU,CAAC,4CAA4C,CAAC;QACxD;QACAkB,UAAU,CAAC,MAAM;UACff,QAAQ,CAAC,aAAa,CAAC;QACzB,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACLH,UAAU,CAACe,IAAI,CAAChB,OAAO,IAAI,eAAe,CAAC;MAC7C;IACF,CAAC,CAAC,OAAOoB,KAAK,EAAE;MACdnB,UAAU,CAAC,kCAAkC,CAAC;MAC9CoB,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;IACvC,CAAC,SAAS;MACRjB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACEZ,OAAA;IAAK+B,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC7BhC,OAAA;MAAAgC,QAAA,EAAI;IAAW;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACpBpC,OAAA;MAAMqC,QAAQ,EAAEvB,YAAa;MAAAkB,QAAA,gBAC3BhC,OAAA;QACEsC,IAAI,EAAC,MAAM;QACXC,WAAW,EAAC,MAAM;QAClBC,KAAK,EAAErC,IAAK;QACZsC,QAAQ,EAAE1B,CAAC,IAAIX,OAAO,CAACW,CAAC,CAAC2B,MAAM,CAACF,KAAK,CAAE;QACvCG,QAAQ;QACRC,QAAQ,EAAEjC;MAAQ;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC,eACFpC,OAAA;QACEsC,IAAI,EAAC,OAAO;QACZC,WAAW,EAAC,OAAO;QACnBC,KAAK,EAAEnC,KAAM;QACboC,QAAQ,EAAE1B,CAAC,IAAIT,QAAQ,CAACS,CAAC,CAAC2B,MAAM,CAACF,KAAK,CAAE;QACxCG,QAAQ;QACRC,QAAQ,EAAEjC;MAAQ;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC,eACFpC,OAAA;QACEsC,IAAI,EAAC,UAAU;QACfC,WAAW,EAAC,UAAU;QACtBC,KAAK,EAAEjC,QAAS;QAChBkC,QAAQ,EAAE1B,CAAC,IAAIP,WAAW,CAACO,CAAC,CAAC2B,MAAM,CAACF,KAAK,CAAE;QAC3CG,QAAQ;QACRC,QAAQ,EAAEjC;MAAQ;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC,eACFpC,OAAA;QAAQsC,IAAI,EAAC,QAAQ;QAACM,QAAQ,EAAEjC,OAAQ;QAAAqB,QAAA,EACrCrB,OAAO,GAAG,qBAAqB,GAAG;MAAQ;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EACN3B,OAAO,iBAAIT,OAAA;MAAG+B,SAAS,EAAEtB,OAAO,CAACoC,QAAQ,CAAC,YAAY,CAAC,GAAG,iBAAiB,GAAG,eAAgB;MAAAb,QAAA,EAAEvB;IAAO;MAAAwB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC1G,CAAC;AAEV,CAAC;AAAClC,EAAA,CA1EID,UAAU;EAAA,QAMGH,WAAW;AAAA;AAAAgD,EAAA,GANxB7C,UAAU;AA4EhB,eAAeA,UAAU;AAAC,IAAA6C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}