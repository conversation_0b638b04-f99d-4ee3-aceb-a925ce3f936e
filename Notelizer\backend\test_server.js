// Test server endpoints
const http = require('http');

function testEndpoint(path, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 5001,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
      }
    };

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const jsonBody = JSON.parse(body);
          resolve({ status: res.statusCode, data: jsonBody });
        } catch (error) {
          resolve({ status: res.statusCode, data: body, error: 'Not JSON' });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

async function runTests() {
  console.log('🧪 Testing server endpoints...\n');

  try {
    // Test health endpoint
    console.log('1. Testing health endpoint...');
    const healthResponse = await testEndpoint('/health');
    console.log('Status:', healthResponse.status);
    console.log('Response:', healthResponse.data);
    
    if (healthResponse.status === 200 && healthResponse.data.status === 'OK') {
      console.log('✅ Health endpoint working\n');
    } else {
      console.log('❌ Health endpoint failed\n');
      return;
    }

    // Test auth signup (create test user)
    console.log('2. Testing auth signup...');
    const signupData = {
      name: 'Test User',
      email: '<EMAIL>',
      password: 'password123',
      role: 'user'
    };
    
    const signupResponse = await testEndpoint('/auth/signup', 'POST', signupData);
    console.log('Signup Status:', signupResponse.status);
    console.log('Signup Response:', signupResponse.data);
    
    if (signupResponse.status === 201 || (signupResponse.status === 400 && signupResponse.data.message?.includes('already exists'))) {
      console.log('✅ Signup working (or user exists)\n');
    } else {
      console.log('❌ Signup failed\n');
      return;
    }

    // Test auth login
    console.log('3. Testing auth login...');
    const loginData = {
      email: '<EMAIL>',
      password: 'password123'
    };
    
    const loginResponse = await testEndpoint('/auth/login', 'POST', loginData);
    console.log('Login Status:', loginResponse.status);
    console.log('Login Response:', loginResponse.data);
    
    if (loginResponse.status === 200 && loginResponse.data.token) {
      console.log('✅ Login working\n');
      
      // Test notes endpoint with token
      console.log('4. Testing notes endpoint...');
      const token = loginResponse.data.token;
      
      const notesResponse = await testEndpoint('/notes', 'GET');
      console.log('Notes Status:', notesResponse.status);
      console.log('Notes Response:', notesResponse.data);
      
      if (notesResponse.status === 401) {
        console.log('✅ Notes endpoint requires authentication (as expected)\n');
      } else {
        console.log('⚠️  Notes endpoint response unexpected\n');
      }
      
    } else {
      console.log('❌ Login failed\n');
    }

    console.log('🎉 Server tests completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

runTests();
