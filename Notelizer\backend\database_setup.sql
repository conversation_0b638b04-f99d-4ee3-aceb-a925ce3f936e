-- Notelizer Database Setup
-- Run these SQL commands to create the required tables

USE Notelizer;

-- Create sections table for custom user sections
CREATE TABLE IF NOT EXISTS sections (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    section_name VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_sections (user_id)
);

-- Create subsections table for organizing content under sections
CREATE TABLE IF NOT EXISTS subsections (
    id INT AUTO_INCREMENT PRIMARY KEY,
    section_id INT NOT NULL,
    subsection_name VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIG<PERSON> KEY (section_id) REFERENCES sections(id) ON DELETE CASCADE,
    INDEX idx_section_subsections (section_id)
);

-- <PERSON>reate subsection_content table for storing notes/links in subsections
CREATE TABLE IF NOT EXISTS subsection_content (
    id INT AUTO_INCREMENT PRIMARY KEY,
    subsection_id INT NOT NULL,
    content_type ENUM('note', 'link') NOT NULL,
    content_text TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (subsection_id) REFERENCES subsections(id) ON DELETE CASCADE,
    INDEX idx_subsection_content (subsection_id)
);

-- Create daily_reviews table for daily reflections and lessons
CREATE TABLE IF NOT EXISTS daily_reviews (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_reviews (user_id),
    INDEX idx_review_date (created_at)
);

-- Update existing tables if needed (these might already exist)

-- Ensure habits table exists with proper structure
CREATE TABLE IF NOT EXISTS habits (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    habit_name VARCHAR(255) NOT NULL,
    is_completed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_habits (user_id)
);

-- Ensure schedule table exists with proper structure
CREATE TABLE IF NOT EXISTS schedule (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    task VARCHAR(255) NOT NULL,
    scheduled_time DATETIME NOT NULL,
    is_done BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_schedule (user_id),
    INDEX idx_scheduled_time (scheduled_time)
);

-- Ensure notes table has proper structure (might already exist)
CREATE TABLE IF NOT EXISTS notes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_notes (user_id),
    INDEX idx_note_timestamp (timestamp)
);

-- Ensure users table exists (should already exist)
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role ENUM('user', 'admin') DEFAULT 'user',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_email (email)
);

-- Insert some sample data for testing (optional)
-- You can uncomment these lines if you want sample data

/*
-- Sample sections
INSERT INTO sections (user_id, section_name) VALUES 
(1, 'Data Structures & Algorithms'),
(1, 'Python Programming'),
(1, 'Web Development');

-- Sample subsections
INSERT INTO subsections (section_id, subsection_name) VALUES 
(1, 'Arrays'),
(1, 'Linked Lists'),
(1, 'Trees'),
(2, 'Basics'),
(2, 'Advanced Topics'),
(3, 'Frontend'),
(3, 'Backend');

-- Sample daily reviews
INSERT INTO daily_reviews (user_id, title, content) VALUES 
(1, 'Today\'s Learning', 'Learned about React hooks and their usage in functional components.'),
(1, 'Reflection', 'Need to practice more algorithm problems, especially tree traversals.');
*/

-- Show table structure for verification
SHOW TABLES;
DESCRIBE sections;
DESCRIBE subsections;
DESCRIBE subsection_content;
DESCRIBE daily_reviews;
