{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\Notelizer\\\\frontend\\\\src\\\\pages\\\\Dashboard.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useAuth } from '../context/AuthContext';\nimport './Dashboard.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  var _sidebarItems$find;\n  const {\n    user\n  } = useAuth();\n  const [activeSection, setActiveSection] = useState('overview');\n  const [showModal, setShowModal] = useState(false);\n  const [modalType, setModalType] = useState('');\n  const [stats, setStats] = useState({\n    notesCount: 0,\n    habitsCount: 0,\n    scheduleCount: 0,\n    sectionsCount: 0,\n    reviewsCount: 0\n  });\n  const [loading, setLoading] = useState(true);\n  const [data, setData] = useState({\n    notes: [],\n    habits: [],\n    schedule: [],\n    sections: [],\n    reviews: []\n  });\n  useEffect(() => {\n    fetchAllData();\n  }, []);\n  const fetchAllData = async () => {\n    try {\n      const token = localStorage.getItem('token');\n\n      // Fetch notes\n      const notesResponse = await fetch('http://localhost:5001/notes', {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      if (notesResponse.ok) {\n        const notes = await notesResponse.json();\n        setData(prev => ({\n          ...prev,\n          notes\n        }));\n        setStats(prev => ({\n          ...prev,\n          notesCount: notes.length\n        }));\n      }\n\n      // Fetch habits\n      const habitsResponse = await fetch('http://localhost:5001/habits', {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      if (habitsResponse.ok) {\n        const habits = await habitsResponse.json();\n        setData(prev => ({\n          ...prev,\n          habits\n        }));\n        setStats(prev => ({\n          ...prev,\n          habitsCount: habits.length\n        }));\n      }\n\n      // Fetch schedule\n      const scheduleResponse = await fetch('http://localhost:5001/schedule', {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      if (scheduleResponse.ok) {\n        const schedule = await scheduleResponse.json();\n        setData(prev => ({\n          ...prev,\n          schedule\n        }));\n        setStats(prev => ({\n          ...prev,\n          scheduleCount: schedule.length\n        }));\n      }\n\n      // Fetch sections\n      const sectionsResponse = await fetch('http://localhost:5001/sections', {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      if (sectionsResponse.ok) {\n        const sections = await sectionsResponse.json();\n        setData(prev => ({\n          ...prev,\n          sections\n        }));\n        setStats(prev => ({\n          ...prev,\n          sectionsCount: sections.length\n        }));\n      }\n\n      // Fetch reviews\n      const reviewsResponse = await fetch('http://localhost:5001/reviews', {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      if (reviewsResponse.ok) {\n        const reviews = await reviewsResponse.json();\n        setData(prev => ({\n          ...prev,\n          reviews\n        }));\n        setStats(prev => ({\n          ...prev,\n          reviewsCount: reviews.length\n        }));\n      }\n    } catch (error) {\n      console.error('Error fetching data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const sidebarItems = [{\n    id: 'overview',\n    label: 'Overview',\n    icon: '📊'\n  }, {\n    id: 'notes',\n    label: 'My Notes',\n    icon: '📝'\n  }, {\n    id: 'habits',\n    label: 'My Habits',\n    icon: '✅'\n  }, {\n    id: 'schedule',\n    label: 'My Schedule',\n    icon: '📅'\n  }, {\n    id: 'sections',\n    label: 'My Sections',\n    icon: '📚'\n  }, {\n    id: 'reviews',\n    label: 'Daily Reviews',\n    icon: '📖'\n  }];\n  const quickActions = [{\n    id: 'note',\n    label: 'Create Note',\n    icon: '📝'\n  }, {\n    id: 'habit',\n    label: 'Add Habit',\n    icon: '✅'\n  }, {\n    id: 'task',\n    label: 'Schedule Task',\n    icon: '📅'\n  }, {\n    id: 'section',\n    label: 'Add Section',\n    icon: '📚'\n  }, {\n    id: 'review',\n    label: 'Daily Review',\n    icon: '📖'\n  }];\n  const handleQuickAction = actionType => {\n    setModalType(actionType);\n    setShowModal(true);\n  };\n  const handleSidebarClick = sectionId => {\n    setActiveSection(sectionId);\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-loading\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-spinner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Loading your dashboard...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"dashboard-layout\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-sidebar\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"sidebar-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Notelizer\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"Welcome, \", (user === null || user === void 0 ? void 0 : user.name) || 'User', \"!\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"sidebar-nav\",\n        children: sidebarItems.map(item => /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `sidebar-item ${activeSection === item.id ? 'active' : ''}`,\n          onClick: () => handleSidebarClick(item.id),\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"sidebar-icon\",\n            children: item.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"sidebar-label\",\n            children: item.label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 15\n          }, this)]\n        }, item.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"sidebar-quick-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          children: \"Quick Actions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this), quickActions.map(action => /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"quick-action-item\",\n          onClick: () => handleQuickAction(action.id),\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"action-icon\",\n            children: action.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"action-label\",\n            children: action.label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 15\n          }, this)]\n        }, action.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 13\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-main\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"main-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: ((_sidebarItems$find = sidebarItems.find(item => item.id === activeSection)) === null || _sidebarItems$find === void 0 ? void 0 : _sidebarItems$find.label) || 'Dashboard'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"date-info\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"current-date\",\n            children: new Date().toLocaleDateString('en-US', {\n              weekday: 'long',\n              year: 'numeric',\n              month: 'long',\n              day: 'numeric'\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"main-content\",\n        children: [activeSection === 'overview' && /*#__PURE__*/_jsxDEV(OverviewSection, {\n          stats: stats\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 44\n        }, this), activeSection === 'notes' && /*#__PURE__*/_jsxDEV(NotesSection, {\n          notes: data.notes,\n          onRefresh: fetchAllData\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 41\n        }, this), activeSection === 'habits' && /*#__PURE__*/_jsxDEV(HabitsSection, {\n          habits: data.habits,\n          onRefresh: fetchAllData\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 42\n        }, this), activeSection === 'schedule' && /*#__PURE__*/_jsxDEV(ScheduleSection, {\n          schedule: data.schedule,\n          onRefresh: fetchAllData\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 44\n        }, this), activeSection === 'sections' && /*#__PURE__*/_jsxDEV(SectionsSection, {\n          sections: data.sections,\n          onRefresh: fetchAllData\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 44\n        }, this), activeSection === 'reviews' && /*#__PURE__*/_jsxDEV(ReviewsSection, {\n          reviews: data.reviews,\n          onRefresh: fetchAllData\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 43\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 169,\n      columnNumber: 7\n    }, this), showModal && /*#__PURE__*/_jsxDEV(Modal, {\n      type: modalType,\n      onClose: () => setShowModal(false),\n      onSuccess: () => {\n        setShowModal(false);\n        fetchAllData();\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 132,\n    columnNumber: 5\n  }, this);\n};\n\n// Overview Section Component\n_s(Dashboard, \"R+Fm9qONJmqKHmhN6adhds3Nf8E=\", false, function () {\n  return [useAuth];\n});\n_c = Dashboard;\nconst OverviewSection = ({\n  stats\n}) => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"overview-section\",\n  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"stats-grid\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stat-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-icon\",\n        children: \"\\uD83D\\uDCDD\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: stats.notesCount\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Notes\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 213,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stat-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-icon\",\n        children: \"\\u2705\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: stats.habitsCount\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Habits\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 222,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 220,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stat-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-icon\",\n        children: \"\\uD83D\\uDCC5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: stats.scheduleCount\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Tasks\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 227,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stat-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-icon\",\n        children: \"\\uD83D\\uDCDA\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: stats.sectionsCount\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Sections\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 236,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 234,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stat-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-icon\",\n        children: \"\\uD83D\\uDCD6\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 242,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: stats.reviewsCount\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Reviews\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 243,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 241,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 212,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"recent-activity\",\n    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n      children: \"Recent Activity\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 250,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: \"Your recent notes, habits, and tasks will appear here.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 251,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 249,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 211,\n  columnNumber: 3\n}, this);\n\n// Notes Section Component\n_c2 = OverviewSection;\nconst NotesSection = ({\n  notes,\n  onRefresh\n}) => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"content-section\",\n  children: notes.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"empty-state\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"empty-icon\",\n      children: \"\\uD83D\\uDCDD\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 261,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n      children: \"No notes yet\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 262,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: \"Create your first note to get started!\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 263,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 260,\n    columnNumber: 7\n  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"content-grid\",\n    children: notes.map(note => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"content-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        children: note.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [note.content.substring(0, 100), \"...\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 270,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"edit-btn\",\n          children: \"Edit\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"delete-btn\",\n          children: \"Delete\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-date\",\n        children: new Date(note.timestamp).toLocaleDateString()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 275,\n        columnNumber: 13\n      }, this)]\n    }, note.id, true, {\n      fileName: _jsxFileName,\n      lineNumber: 268,\n      columnNumber: 11\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 266,\n    columnNumber: 7\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 258,\n  columnNumber: 3\n}, this);\n\n// Habits Section Component\n_c3 = NotesSection;\nconst HabitsSection = ({\n  habits,\n  onRefresh\n}) => {\n  const toggleHabit = async habitId => {\n    try {\n      const token = localStorage.getItem('token');\n      const response = await fetch(`http://localhost:5001/habits/${habitId}/toggle`, {\n        method: 'PUT',\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      if (response.ok) {\n        onRefresh(); // Refresh the data\n      } else {\n        console.error('Failed to toggle habit');\n      }\n    } catch (error) {\n      console.error('Error toggling habit:', error);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"content-section\",\n    children: habits.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"empty-state\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"empty-icon\",\n        children: \"\\u2705\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 309,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"No habits yet\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 310,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Add your first habit to start tracking!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 311,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 308,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"habits-list\",\n      children: habits.map(habit => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"habit-item\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"habit-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: habit.habit_name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Track your daily progress\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"habit-toggle\",\n          children: /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            checked: habit.is_completed,\n            onChange: () => toggleHabit(habit.id)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 15\n        }, this)]\n      }, habit.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 316,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 314,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 306,\n    columnNumber: 5\n  }, this);\n};\n\n// Schedule Section Component\n_c4 = HabitsSection;\nconst ScheduleSection = ({\n  schedule,\n  onRefresh\n}) => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"content-section\",\n  children: schedule.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"empty-state\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"empty-icon\",\n      children: \"\\uD83D\\uDCC5\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 341,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n      children: \"No scheduled tasks\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 342,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: \"Schedule your first task to stay organized!\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 343,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 340,\n    columnNumber: 7\n  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"schedule-list\",\n    children: schedule.map(task => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"schedule-item\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"task-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          children: task.task\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: new Date(task.scheduled_time).toLocaleString()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 349,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"task-status\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: `status ${task.is_done ? 'completed' : 'pending'}`,\n          children: task.is_done ? 'Completed' : 'Pending'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 354,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 353,\n        columnNumber: 13\n      }, this)]\n    }, task.id, true, {\n      fileName: _jsxFileName,\n      lineNumber: 348,\n      columnNumber: 11\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 346,\n    columnNumber: 7\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 338,\n  columnNumber: 3\n}, this);\n\n// Sections Section Component\n_c5 = ScheduleSection;\nconst SectionsSection = ({\n  sections,\n  onRefresh\n}) => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"content-section\",\n  children: sections.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"empty-state\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"empty-icon\",\n      children: \"\\uD83D\\uDCDA\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 370,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n      children: \"No sections yet\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 371,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: \"Create your first section to organize your topics!\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 372,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 369,\n    columnNumber: 7\n  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"sections-list\",\n    children: sections.map(section => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"section-item\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        children: section.section_name\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 378,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Click to view subsections\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 379,\n        columnNumber: 13\n      }, this)]\n    }, section.id, true, {\n      fileName: _jsxFileName,\n      lineNumber: 377,\n      columnNumber: 11\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 375,\n    columnNumber: 7\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 367,\n  columnNumber: 3\n}, this);\n\n// Reviews Section Component\n_c6 = SectionsSection;\nconst ReviewsSection = ({\n  reviews,\n  onRefresh\n}) => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"content-section\",\n  children: reviews.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"empty-state\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"empty-icon\",\n      children: \"\\uD83D\\uDCD6\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 392,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n      children: \"No daily reviews yet\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 393,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: \"Start writing your daily reflections!\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 394,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 391,\n    columnNumber: 7\n  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"reviews-list\",\n    children: reviews.map(review => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"review-item\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        children: review.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 400,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [review.content.substring(0, 150), \"...\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 401,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"review-date\",\n        children: new Date(review.created_at).toLocaleDateString()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 402,\n        columnNumber: 13\n      }, this)]\n    }, review.id, true, {\n      fileName: _jsxFileName,\n      lineNumber: 399,\n      columnNumber: 11\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 397,\n    columnNumber: 7\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 389,\n  columnNumber: 3\n}, this);\n\n// Modal Component\n_c7 = ReviewsSection;\nconst Modal = ({\n  type,\n  onClose,\n  onSuccess\n}) => {\n  _s2();\n  const [formData, setFormData] = useState({});\n  const [loading, setLoading] = useState(false);\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    try {\n      const token = localStorage.getItem('token');\n      let url = '';\n      let body = {};\n      switch (type) {\n        case 'note':\n          url = 'http://localhost:5001/notes';\n          body = {\n            title: formData.title,\n            content: formData.content\n          };\n          break;\n        case 'habit':\n          url = 'http://localhost:5001/habits';\n          body = {\n            habit_name: formData.name\n          };\n          break;\n        case 'task':\n          url = 'http://localhost:5001/schedule';\n          body = {\n            task: formData.task,\n            scheduled_time: formData.scheduled_time\n          };\n          break;\n        case 'section':\n          url = 'http://localhost:5001/sections';\n          body = {\n            section_name: formData.section_name\n          };\n          break;\n        case 'review':\n          url = 'http://localhost:5001/reviews';\n          body = {\n            title: formData.title,\n            content: formData.content\n          };\n          break;\n        default:\n          throw new Error('Unknown type');\n      }\n      const response = await fetch(url, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${token}`\n        },\n        body: JSON.stringify(body)\n      });\n      if (response.ok) {\n        onSuccess();\n      } else {\n        const errorData = await response.json();\n        throw new Error(errorData.message || 'Failed to create item');\n      }\n    } catch (error) {\n      console.error('Error:', error);\n      alert('Error: ' + error.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const renderForm = () => {\n    switch (type) {\n      case 'note':\n        return /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Note title\",\n            value: formData.title || '',\n            onChange: e => setFormData({\n              ...formData,\n              title: e.target.value\n            }),\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 479,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            placeholder: \"Note content\",\n            value: formData.content || '',\n            onChange: e => setFormData({\n              ...formData,\n              content: e.target.value\n            }),\n            rows: \"4\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 486,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true);\n      case 'habit':\n        return /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          placeholder: \"Habit name\",\n          value: formData.name || '',\n          onChange: e => setFormData({\n            ...formData,\n            name: e.target.value\n          }),\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 497,\n          columnNumber: 11\n        }, this);\n      case 'task':\n        return /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Task description\",\n            value: formData.task || '',\n            onChange: e => setFormData({\n              ...formData,\n              task: e.target.value\n            }),\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 508,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"datetime-local\",\n            value: formData.scheduled_time || '',\n            onChange: e => setFormData({\n              ...formData,\n              scheduled_time: e.target.value\n            }),\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 515,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true);\n      case 'section':\n        return /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          placeholder: \"Section name\",\n          value: formData.section_name || '',\n          onChange: e => setFormData({\n            ...formData,\n            section_name: e.target.value\n          }),\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 525,\n          columnNumber: 11\n        }, this);\n      case 'review':\n        return /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Review title\",\n            value: formData.title || '',\n            onChange: e => setFormData({\n              ...formData,\n              title: e.target.value\n            }),\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 536,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            placeholder: \"Your daily reflection...\",\n            value: formData.content || '',\n            onChange: e => setFormData({\n              ...formData,\n              content: e.target.value\n            }),\n            rows: \"4\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 543,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true);\n      default:\n        return null;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"modal-overlay\",\n    onClick: onClose,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-content\",\n      onClick: e => e.stopPropagation(),\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: [\"Create \", type.charAt(0).toUpperCase() + type.slice(1)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 561,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"modal-close\",\n          onClick: onClose,\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 562,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 560,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"modal-form\",\n        children: [renderForm(), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: onClose,\n            className: \"cancel-btn\",\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 567,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            disabled: loading,\n            className: \"submit-btn\",\n            children: loading ? 'Creating...' : 'Create'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 570,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 566,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 564,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 559,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 558,\n    columnNumber: 5\n  }, this);\n};\n_s2(Modal, \"aKbyWoXTTL1HyXbuBINaFJ9d+rc=\");\n_c8 = Modal;\nexport default Dashboard;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8;\n$RefreshReg$(_c, \"Dashboard\");\n$RefreshReg$(_c2, \"OverviewSection\");\n$RefreshReg$(_c3, \"NotesSection\");\n$RefreshReg$(_c4, \"HabitsSection\");\n$RefreshReg$(_c5, \"ScheduleSection\");\n$RefreshReg$(_c6, \"SectionsSection\");\n$RefreshReg$(_c7, \"ReviewsSection\");\n$RefreshReg$(_c8, \"Modal\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Dashboard", "_s", "_sidebarItems$find", "user", "activeSection", "setActiveSection", "showModal", "setShowModal", "modalType", "setModalType", "stats", "setStats", "notesCount", "habitsCount", "scheduleCount", "sectionsCount", "reviewsCount", "loading", "setLoading", "data", "setData", "notes", "habits", "schedule", "sections", "reviews", "fetchAllData", "token", "localStorage", "getItem", "notesResponse", "fetch", "headers", "ok", "json", "prev", "length", "habitsResponse", "scheduleResponse", "sectionsResponse", "reviewsResponse", "error", "console", "sidebarItems", "id", "label", "icon", "quickActions", "handleQuickAction", "actionType", "handleSidebarClick", "sectionId", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "map", "item", "onClick", "action", "find", "Date", "toLocaleDateString", "weekday", "year", "month", "day", "OverviewSection", "NotesSection", "onRefresh", "HabitsSection", "ScheduleSection", "SectionsSection", "ReviewsSection", "Modal", "type", "onClose", "onSuccess", "_c", "_c2", "note", "title", "content", "substring", "timestamp", "_c3", "toggleHabit", "habitId", "response", "method", "habit", "habit_name", "checked", "is_completed", "onChange", "_c4", "task", "scheduled_time", "toLocaleString", "is_done", "_c5", "section", "section_name", "_c6", "review", "created_at", "_c7", "_s2", "formData", "setFormData", "handleSubmit", "e", "preventDefault", "url", "body", "Error", "JSON", "stringify", "errorData", "message", "alert", "renderForm", "placeholder", "value", "target", "required", "rows", "stopPropagation", "char<PERSON>t", "toUpperCase", "slice", "onSubmit", "disabled", "_c8", "$RefreshReg$"], "sources": ["D:/Projects/Notelizer/frontend/src/pages/Dashboard.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useAuth } from '../context/AuthContext';\nimport './Dashboard.css';\n\nconst Dashboard = () => {\n  const { user } = useAuth();\n  const [activeSection, setActiveSection] = useState('overview');\n  const [showModal, setShowModal] = useState(false);\n  const [modalType, setModalType] = useState('');\n  const [stats, setStats] = useState({\n    notesCount: 0,\n    habitsCount: 0,\n    scheduleCount: 0,\n    sectionsCount: 0,\n    reviewsCount: 0\n  });\n  const [loading, setLoading] = useState(true);\n  const [data, setData] = useState({\n    notes: [],\n    habits: [],\n    schedule: [],\n    sections: [],\n    reviews: []\n  });\n\n  useEffect(() => {\n    fetchAllData();\n  }, []);\n\n  const fetchAllData = async () => {\n    try {\n      const token = localStorage.getItem('token');\n\n      // Fetch notes\n      const notesResponse = await fetch('http://localhost:5001/notes', {\n        headers: { 'Authorization': `Bearer ${token}` }\n      });\n\n      if (notesResponse.ok) {\n        const notes = await notesResponse.json();\n        setData(prev => ({ ...prev, notes }));\n        setStats(prev => ({ ...prev, notesCount: notes.length }));\n      }\n\n      // Fetch habits\n      const habitsResponse = await fetch('http://localhost:5001/habits', {\n        headers: { 'Authorization': `Bearer ${token}` }\n      });\n\n      if (habitsResponse.ok) {\n        const habits = await habitsResponse.json();\n        setData(prev => ({ ...prev, habits }));\n        setStats(prev => ({ ...prev, habitsCount: habits.length }));\n      }\n\n      // Fetch schedule\n      const scheduleResponse = await fetch('http://localhost:5001/schedule', {\n        headers: { 'Authorization': `Bearer ${token}` }\n      });\n\n      if (scheduleResponse.ok) {\n        const schedule = await scheduleResponse.json();\n        setData(prev => ({ ...prev, schedule }));\n        setStats(prev => ({ ...prev, scheduleCount: schedule.length }));\n      }\n\n      // Fetch sections\n      const sectionsResponse = await fetch('http://localhost:5001/sections', {\n        headers: { 'Authorization': `Bearer ${token}` }\n      });\n\n      if (sectionsResponse.ok) {\n        const sections = await sectionsResponse.json();\n        setData(prev => ({ ...prev, sections }));\n        setStats(prev => ({ ...prev, sectionsCount: sections.length }));\n      }\n\n      // Fetch reviews\n      const reviewsResponse = await fetch('http://localhost:5001/reviews', {\n        headers: { 'Authorization': `Bearer ${token}` }\n      });\n\n      if (reviewsResponse.ok) {\n        const reviews = await reviewsResponse.json();\n        setData(prev => ({ ...prev, reviews }));\n        setStats(prev => ({ ...prev, reviewsCount: reviews.length }));\n      }\n\n    } catch (error) {\n      console.error('Error fetching data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const sidebarItems = [\n    { id: 'overview', label: 'Overview', icon: '📊' },\n    { id: 'notes', label: 'My Notes', icon: '📝' },\n    { id: 'habits', label: 'My Habits', icon: '✅' },\n    { id: 'schedule', label: 'My Schedule', icon: '📅' },\n    { id: 'sections', label: 'My Sections', icon: '📚' },\n    { id: 'reviews', label: 'Daily Reviews', icon: '📖' }\n  ];\n\n  const quickActions = [\n    { id: 'note', label: 'Create Note', icon: '📝' },\n    { id: 'habit', label: 'Add Habit', icon: '✅' },\n    { id: 'task', label: 'Schedule Task', icon: '📅' },\n    { id: 'section', label: 'Add Section', icon: '📚' },\n    { id: 'review', label: 'Daily Review', icon: '📖' }\n  ];\n\n  const handleQuickAction = (actionType) => {\n    setModalType(actionType);\n    setShowModal(true);\n  };\n\n  const handleSidebarClick = (sectionId) => {\n    setActiveSection(sectionId);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"dashboard-loading\">\n        <div className=\"loading-spinner\"></div>\n        <p>Loading your dashboard...</p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"dashboard-layout\">\n      {/* Sidebar */}\n      <div className=\"dashboard-sidebar\">\n        <div className=\"sidebar-header\">\n          <h2>Notelizer</h2>\n          <p>Welcome, {user?.name || 'User'}!</p>\n        </div>\n\n        <div className=\"sidebar-nav\">\n          {sidebarItems.map((item) => (\n            <button\n              key={item.id}\n              className={`sidebar-item ${activeSection === item.id ? 'active' : ''}`}\n              onClick={() => handleSidebarClick(item.id)}\n            >\n              <span className=\"sidebar-icon\">{item.icon}</span>\n              <span className=\"sidebar-label\">{item.label}</span>\n            </button>\n          ))}\n        </div>\n\n        <div className=\"sidebar-quick-actions\">\n          <h4>Quick Actions</h4>\n          {quickActions.map((action) => (\n            <button\n              key={action.id}\n              className=\"quick-action-item\"\n              onClick={() => handleQuickAction(action.id)}\n            >\n              <span className=\"action-icon\">{action.icon}</span>\n              <span className=\"action-label\">{action.label}</span>\n            </button>\n          ))}\n        </div>\n      </div>\n\n      {/* Main Content */}\n      <div className=\"dashboard-main\">\n        <div className=\"main-header\">\n          <h1>{sidebarItems.find(item => item.id === activeSection)?.label || 'Dashboard'}</h1>\n          <div className=\"date-info\">\n            <span className=\"current-date\">\n              {new Date().toLocaleDateString('en-US', {\n                weekday: 'long',\n                year: 'numeric',\n                month: 'long',\n                day: 'numeric'\n              })}\n            </span>\n          </div>\n        </div>\n\n        <div className=\"main-content\">\n          {activeSection === 'overview' && <OverviewSection stats={stats} />}\n          {activeSection === 'notes' && <NotesSection notes={data.notes} onRefresh={fetchAllData} />}\n          {activeSection === 'habits' && <HabitsSection habits={data.habits} onRefresh={fetchAllData} />}\n          {activeSection === 'schedule' && <ScheduleSection schedule={data.schedule} onRefresh={fetchAllData} />}\n          {activeSection === 'sections' && <SectionsSection sections={data.sections} onRefresh={fetchAllData} />}\n          {activeSection === 'reviews' && <ReviewsSection reviews={data.reviews} onRefresh={fetchAllData} />}\n        </div>\n      </div>\n\n      {/* Modal for Quick Actions */}\n      {showModal && (\n        <Modal\n          type={modalType}\n          onClose={() => setShowModal(false)}\n          onSuccess={() => {\n            setShowModal(false);\n            fetchAllData();\n          }}\n        />\n      )}\n    </div>\n  );\n};\n\n// Overview Section Component\nconst OverviewSection = ({ stats }) => (\n  <div className=\"overview-section\">\n    <div className=\"stats-grid\">\n      <div className=\"stat-card\">\n        <div className=\"stat-icon\">📝</div>\n        <div className=\"stat-info\">\n          <h3>{stats.notesCount}</h3>\n          <p>Notes</p>\n        </div>\n      </div>\n      <div className=\"stat-card\">\n        <div className=\"stat-icon\">✅</div>\n        <div className=\"stat-info\">\n          <h3>{stats.habitsCount}</h3>\n          <p>Habits</p>\n        </div>\n      </div>\n      <div className=\"stat-card\">\n        <div className=\"stat-icon\">📅</div>\n        <div className=\"stat-info\">\n          <h3>{stats.scheduleCount}</h3>\n          <p>Tasks</p>\n        </div>\n      </div>\n      <div className=\"stat-card\">\n        <div className=\"stat-icon\">📚</div>\n        <div className=\"stat-info\">\n          <h3>{stats.sectionsCount}</h3>\n          <p>Sections</p>\n        </div>\n      </div>\n      <div className=\"stat-card\">\n        <div className=\"stat-icon\">📖</div>\n        <div className=\"stat-info\">\n          <h3>{stats.reviewsCount}</h3>\n          <p>Reviews</p>\n        </div>\n      </div>\n    </div>\n    <div className=\"recent-activity\">\n      <h3>Recent Activity</h3>\n      <p>Your recent notes, habits, and tasks will appear here.</p>\n    </div>\n  </div>\n);\n\n// Notes Section Component\nconst NotesSection = ({ notes, onRefresh }) => (\n  <div className=\"content-section\">\n    {notes.length === 0 ? (\n      <div className=\"empty-state\">\n        <div className=\"empty-icon\">📝</div>\n        <h3>No notes yet</h3>\n        <p>Create your first note to get started!</p>\n      </div>\n    ) : (\n      <div className=\"content-grid\">\n        {notes.map((note) => (\n          <div key={note.id} className=\"content-card\">\n            <h4>{note.title}</h4>\n            <p>{note.content.substring(0, 100)}...</p>\n            <div className=\"card-actions\">\n              <button className=\"edit-btn\">Edit</button>\n              <button className=\"delete-btn\">Delete</button>\n            </div>\n            <div className=\"card-date\">\n              {new Date(note.timestamp).toLocaleDateString()}\n            </div>\n          </div>\n        ))}\n      </div>\n    )}\n  </div>\n);\n\n// Habits Section Component\nconst HabitsSection = ({ habits, onRefresh }) => {\n  const toggleHabit = async (habitId) => {\n    try {\n      const token = localStorage.getItem('token');\n      const response = await fetch(`http://localhost:5001/habits/${habitId}/toggle`, {\n        method: 'PUT',\n        headers: { 'Authorization': `Bearer ${token}` }\n      });\n\n      if (response.ok) {\n        onRefresh(); // Refresh the data\n      } else {\n        console.error('Failed to toggle habit');\n      }\n    } catch (error) {\n      console.error('Error toggling habit:', error);\n    }\n  };\n\n  return (\n    <div className=\"content-section\">\n      {habits.length === 0 ? (\n        <div className=\"empty-state\">\n          <div className=\"empty-icon\">✅</div>\n          <h3>No habits yet</h3>\n          <p>Add your first habit to start tracking!</p>\n        </div>\n      ) : (\n        <div className=\"habits-list\">\n          {habits.map((habit) => (\n            <div key={habit.id} className=\"habit-item\">\n              <div className=\"habit-info\">\n                <h4>{habit.habit_name}</h4>\n                <p>Track your daily progress</p>\n              </div>\n              <div className=\"habit-toggle\">\n                <input\n                  type=\"checkbox\"\n                  checked={habit.is_completed}\n                  onChange={() => toggleHabit(habit.id)}\n                />\n              </div>\n            </div>\n          ))}\n        </div>\n      )}\n    </div>\n  );\n};\n\n// Schedule Section Component\nconst ScheduleSection = ({ schedule, onRefresh }) => (\n  <div className=\"content-section\">\n    {schedule.length === 0 ? (\n      <div className=\"empty-state\">\n        <div className=\"empty-icon\">📅</div>\n        <h3>No scheduled tasks</h3>\n        <p>Schedule your first task to stay organized!</p>\n      </div>\n    ) : (\n      <div className=\"schedule-list\">\n        {schedule.map((task) => (\n          <div key={task.id} className=\"schedule-item\">\n            <div className=\"task-info\">\n              <h4>{task.task}</h4>\n              <p>{new Date(task.scheduled_time).toLocaleString()}</p>\n            </div>\n            <div className=\"task-status\">\n              <span className={`status ${task.is_done ? 'completed' : 'pending'}`}>\n                {task.is_done ? 'Completed' : 'Pending'}\n              </span>\n            </div>\n          </div>\n        ))}\n      </div>\n    )}\n  </div>\n);\n\n// Sections Section Component\nconst SectionsSection = ({ sections, onRefresh }) => (\n  <div className=\"content-section\">\n    {sections.length === 0 ? (\n      <div className=\"empty-state\">\n        <div className=\"empty-icon\">📚</div>\n        <h3>No sections yet</h3>\n        <p>Create your first section to organize your topics!</p>\n      </div>\n    ) : (\n      <div className=\"sections-list\">\n        {sections.map((section) => (\n          <div key={section.id} className=\"section-item\">\n            <h4>{section.section_name}</h4>\n            <p>Click to view subsections</p>\n          </div>\n        ))}\n      </div>\n    )}\n  </div>\n);\n\n// Reviews Section Component\nconst ReviewsSection = ({ reviews, onRefresh }) => (\n  <div className=\"content-section\">\n    {reviews.length === 0 ? (\n      <div className=\"empty-state\">\n        <div className=\"empty-icon\">📖</div>\n        <h3>No daily reviews yet</h3>\n        <p>Start writing your daily reflections!</p>\n      </div>\n    ) : (\n      <div className=\"reviews-list\">\n        {reviews.map((review) => (\n          <div key={review.id} className=\"review-item\">\n            <h4>{review.title}</h4>\n            <p>{review.content.substring(0, 150)}...</p>\n            <div className=\"review-date\">\n              {new Date(review.created_at).toLocaleDateString()}\n            </div>\n          </div>\n        ))}\n      </div>\n    )}\n  </div>\n);\n\n// Modal Component\nconst Modal = ({ type, onClose, onSuccess }) => {\n  const [formData, setFormData] = useState({});\n  const [loading, setLoading] = useState(false);\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n\n    try {\n      const token = localStorage.getItem('token');\n      let url = '';\n      let body = {};\n\n      switch (type) {\n        case 'note':\n          url = 'http://localhost:5001/notes';\n          body = { title: formData.title, content: formData.content };\n          break;\n        case 'habit':\n          url = 'http://localhost:5001/habits';\n          body = { habit_name: formData.name };\n          break;\n        case 'task':\n          url = 'http://localhost:5001/schedule';\n          body = { task: formData.task, scheduled_time: formData.scheduled_time };\n          break;\n        case 'section':\n          url = 'http://localhost:5001/sections';\n          body = { section_name: formData.section_name };\n          break;\n        case 'review':\n          url = 'http://localhost:5001/reviews';\n          body = { title: formData.title, content: formData.content };\n          break;\n        default:\n          throw new Error('Unknown type');\n      }\n\n      const response = await fetch(url, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${token}`\n        },\n        body: JSON.stringify(body)\n      });\n\n      if (response.ok) {\n        onSuccess();\n      } else {\n        const errorData = await response.json();\n        throw new Error(errorData.message || 'Failed to create item');\n      }\n    } catch (error) {\n      console.error('Error:', error);\n      alert('Error: ' + error.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const renderForm = () => {\n    switch (type) {\n      case 'note':\n        return (\n          <>\n            <input\n              type=\"text\"\n              placeholder=\"Note title\"\n              value={formData.title || ''}\n              onChange={(e) => setFormData({...formData, title: e.target.value})}\n              required\n            />\n            <textarea\n              placeholder=\"Note content\"\n              value={formData.content || ''}\n              onChange={(e) => setFormData({...formData, content: e.target.value})}\n              rows=\"4\"\n              required\n            />\n          </>\n        );\n      case 'habit':\n        return (\n          <input\n            type=\"text\"\n            placeholder=\"Habit name\"\n            value={formData.name || ''}\n            onChange={(e) => setFormData({...formData, name: e.target.value})}\n            required\n          />\n        );\n      case 'task':\n        return (\n          <>\n            <input\n              type=\"text\"\n              placeholder=\"Task description\"\n              value={formData.task || ''}\n              onChange={(e) => setFormData({...formData, task: e.target.value})}\n              required\n            />\n            <input\n              type=\"datetime-local\"\n              value={formData.scheduled_time || ''}\n              onChange={(e) => setFormData({...formData, scheduled_time: e.target.value})}\n              required\n            />\n          </>\n        );\n      case 'section':\n        return (\n          <input\n            type=\"text\"\n            placeholder=\"Section name\"\n            value={formData.section_name || ''}\n            onChange={(e) => setFormData({...formData, section_name: e.target.value})}\n            required\n          />\n        );\n      case 'review':\n        return (\n          <>\n            <input\n              type=\"text\"\n              placeholder=\"Review title\"\n              value={formData.title || ''}\n              onChange={(e) => setFormData({...formData, title: e.target.value})}\n              required\n            />\n            <textarea\n              placeholder=\"Your daily reflection...\"\n              value={formData.content || ''}\n              onChange={(e) => setFormData({...formData, content: e.target.value})}\n              rows=\"4\"\n              required\n            />\n          </>\n        );\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <div className=\"modal-overlay\" onClick={onClose}>\n      <div className=\"modal-content\" onClick={(e) => e.stopPropagation()}>\n        <div className=\"modal-header\">\n          <h3>Create {type.charAt(0).toUpperCase() + type.slice(1)}</h3>\n          <button className=\"modal-close\" onClick={onClose}>×</button>\n        </div>\n        <form onSubmit={handleSubmit} className=\"modal-form\">\n          {renderForm()}\n          <div className=\"modal-actions\">\n            <button type=\"button\" onClick={onClose} className=\"cancel-btn\">\n              Cancel\n            </button>\n            <button type=\"submit\" disabled={loading} className=\"submit-btn\">\n              {loading ? 'Creating...' : 'Create'}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAO,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEzB,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,kBAAA;EACtB,MAAM;IAAEC;EAAK,CAAC,GAAGR,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACS,aAAa,EAAEC,gBAAgB,CAAC,GAAGZ,QAAQ,CAAC,UAAU,CAAC;EAC9D,MAAM,CAACa,SAAS,EAAEC,YAAY,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACe,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACiB,KAAK,EAAEC,QAAQ,CAAC,GAAGlB,QAAQ,CAAC;IACjCmB,UAAU,EAAE,CAAC;IACbC,WAAW,EAAE,CAAC;IACdC,aAAa,EAAE,CAAC;IAChBC,aAAa,EAAE,CAAC;IAChBC,YAAY,EAAE;EAChB,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC0B,IAAI,EAAEC,OAAO,CAAC,GAAG3B,QAAQ,CAAC;IAC/B4B,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,OAAO,EAAE;EACX,CAAC,CAAC;EAEF/B,SAAS,CAAC,MAAM;IACdgC,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;;MAE3C;MACA,MAAMC,aAAa,GAAG,MAAMC,KAAK,CAAC,6BAA6B,EAAE;QAC/DC,OAAO,EAAE;UAAE,eAAe,EAAE,UAAUL,KAAK;QAAG;MAChD,CAAC,CAAC;MAEF,IAAIG,aAAa,CAACG,EAAE,EAAE;QACpB,MAAMZ,KAAK,GAAG,MAAMS,aAAa,CAACI,IAAI,CAAC,CAAC;QACxCd,OAAO,CAACe,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAEd;QAAM,CAAC,CAAC,CAAC;QACrCV,QAAQ,CAACwB,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAEvB,UAAU,EAAES,KAAK,CAACe;QAAO,CAAC,CAAC,CAAC;MAC3D;;MAEA;MACA,MAAMC,cAAc,GAAG,MAAMN,KAAK,CAAC,8BAA8B,EAAE;QACjEC,OAAO,EAAE;UAAE,eAAe,EAAE,UAAUL,KAAK;QAAG;MAChD,CAAC,CAAC;MAEF,IAAIU,cAAc,CAACJ,EAAE,EAAE;QACrB,MAAMX,MAAM,GAAG,MAAMe,cAAc,CAACH,IAAI,CAAC,CAAC;QAC1Cd,OAAO,CAACe,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAEb;QAAO,CAAC,CAAC,CAAC;QACtCX,QAAQ,CAACwB,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAEtB,WAAW,EAAES,MAAM,CAACc;QAAO,CAAC,CAAC,CAAC;MAC7D;;MAEA;MACA,MAAME,gBAAgB,GAAG,MAAMP,KAAK,CAAC,gCAAgC,EAAE;QACrEC,OAAO,EAAE;UAAE,eAAe,EAAE,UAAUL,KAAK;QAAG;MAChD,CAAC,CAAC;MAEF,IAAIW,gBAAgB,CAACL,EAAE,EAAE;QACvB,MAAMV,QAAQ,GAAG,MAAMe,gBAAgB,CAACJ,IAAI,CAAC,CAAC;QAC9Cd,OAAO,CAACe,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAEZ;QAAS,CAAC,CAAC,CAAC;QACxCZ,QAAQ,CAACwB,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAErB,aAAa,EAAES,QAAQ,CAACa;QAAO,CAAC,CAAC,CAAC;MACjE;;MAEA;MACA,MAAMG,gBAAgB,GAAG,MAAMR,KAAK,CAAC,gCAAgC,EAAE;QACrEC,OAAO,EAAE;UAAE,eAAe,EAAE,UAAUL,KAAK;QAAG;MAChD,CAAC,CAAC;MAEF,IAAIY,gBAAgB,CAACN,EAAE,EAAE;QACvB,MAAMT,QAAQ,GAAG,MAAMe,gBAAgB,CAACL,IAAI,CAAC,CAAC;QAC9Cd,OAAO,CAACe,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAEX;QAAS,CAAC,CAAC,CAAC;QACxCb,QAAQ,CAACwB,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAEpB,aAAa,EAAES,QAAQ,CAACY;QAAO,CAAC,CAAC,CAAC;MACjE;;MAEA;MACA,MAAMI,eAAe,GAAG,MAAMT,KAAK,CAAC,+BAA+B,EAAE;QACnEC,OAAO,EAAE;UAAE,eAAe,EAAE,UAAUL,KAAK;QAAG;MAChD,CAAC,CAAC;MAEF,IAAIa,eAAe,CAACP,EAAE,EAAE;QACtB,MAAMR,OAAO,GAAG,MAAMe,eAAe,CAACN,IAAI,CAAC,CAAC;QAC5Cd,OAAO,CAACe,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAEV;QAAQ,CAAC,CAAC,CAAC;QACvCd,QAAQ,CAACwB,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAEnB,YAAY,EAAES,OAAO,CAACW;QAAO,CAAC,CAAC,CAAC;MAC/D;IAEF,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC9C,CAAC,SAAS;MACRvB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMyB,YAAY,GAAG,CACnB;IAAEC,EAAE,EAAE,UAAU;IAAEC,KAAK,EAAE,UAAU;IAAEC,IAAI,EAAE;EAAK,CAAC,EACjD;IAAEF,EAAE,EAAE,OAAO;IAAEC,KAAK,EAAE,UAAU;IAAEC,IAAI,EAAE;EAAK,CAAC,EAC9C;IAAEF,EAAE,EAAE,QAAQ;IAAEC,KAAK,EAAE,WAAW;IAAEC,IAAI,EAAE;EAAI,CAAC,EAC/C;IAAEF,EAAE,EAAE,UAAU;IAAEC,KAAK,EAAE,aAAa;IAAEC,IAAI,EAAE;EAAK,CAAC,EACpD;IAAEF,EAAE,EAAE,UAAU;IAAEC,KAAK,EAAE,aAAa;IAAEC,IAAI,EAAE;EAAK,CAAC,EACpD;IAAEF,EAAE,EAAE,SAAS;IAAEC,KAAK,EAAE,eAAe;IAAEC,IAAI,EAAE;EAAK,CAAC,CACtD;EAED,MAAMC,YAAY,GAAG,CACnB;IAAEH,EAAE,EAAE,MAAM;IAAEC,KAAK,EAAE,aAAa;IAAEC,IAAI,EAAE;EAAK,CAAC,EAChD;IAAEF,EAAE,EAAE,OAAO;IAAEC,KAAK,EAAE,WAAW;IAAEC,IAAI,EAAE;EAAI,CAAC,EAC9C;IAAEF,EAAE,EAAE,MAAM;IAAEC,KAAK,EAAE,eAAe;IAAEC,IAAI,EAAE;EAAK,CAAC,EAClD;IAAEF,EAAE,EAAE,SAAS;IAAEC,KAAK,EAAE,aAAa;IAAEC,IAAI,EAAE;EAAK,CAAC,EACnD;IAAEF,EAAE,EAAE,QAAQ;IAAEC,KAAK,EAAE,cAAc;IAAEC,IAAI,EAAE;EAAK,CAAC,CACpD;EAED,MAAME,iBAAiB,GAAIC,UAAU,IAAK;IACxCxC,YAAY,CAACwC,UAAU,CAAC;IACxB1C,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAM2C,kBAAkB,GAAIC,SAAS,IAAK;IACxC9C,gBAAgB,CAAC8C,SAAS,CAAC;EAC7B,CAAC;EAED,IAAIlC,OAAO,EAAE;IACX,oBACEpB,OAAA;MAAKuD,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCxD,OAAA;QAAKuD,SAAS,EAAC;MAAiB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACvC5D,OAAA;QAAAwD,QAAA,EAAG;MAAyB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B,CAAC;EAEV;EAEA,oBACE5D,OAAA;IAAKuD,SAAS,EAAC,kBAAkB;IAAAC,QAAA,gBAE/BxD,OAAA;MAAKuD,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCxD,OAAA;QAAKuD,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BxD,OAAA;UAAAwD,QAAA,EAAI;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClB5D,OAAA;UAAAwD,QAAA,GAAG,WAAS,EAAC,CAAAlD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuD,IAAI,KAAI,MAAM,EAAC,GAAC;QAAA;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC,eAEN5D,OAAA;QAAKuD,SAAS,EAAC,aAAa;QAAAC,QAAA,EACzBV,YAAY,CAACgB,GAAG,CAAEC,IAAI,iBACrB/D,OAAA;UAEEuD,SAAS,EAAE,gBAAgBhD,aAAa,KAAKwD,IAAI,CAAChB,EAAE,GAAG,QAAQ,GAAG,EAAE,EAAG;UACvEiB,OAAO,EAAEA,CAAA,KAAMX,kBAAkB,CAACU,IAAI,CAAChB,EAAE,CAAE;UAAAS,QAAA,gBAE3CxD,OAAA;YAAMuD,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAEO,IAAI,CAACd;UAAI;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACjD5D,OAAA;YAAMuD,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAEO,IAAI,CAACf;UAAK;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA,GAL9CG,IAAI,CAAChB,EAAE;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAMN,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEN5D,OAAA;QAAKuD,SAAS,EAAC,uBAAuB;QAAAC,QAAA,gBACpCxD,OAAA;UAAAwD,QAAA,EAAI;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EACrBV,YAAY,CAACY,GAAG,CAAEG,MAAM,iBACvBjE,OAAA;UAEEuD,SAAS,EAAC,mBAAmB;UAC7BS,OAAO,EAAEA,CAAA,KAAMb,iBAAiB,CAACc,MAAM,CAAClB,EAAE,CAAE;UAAAS,QAAA,gBAE5CxD,OAAA;YAAMuD,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAES,MAAM,CAAChB;UAAI;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAClD5D,OAAA;YAAMuD,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAES,MAAM,CAACjB;UAAK;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA,GAL/CK,MAAM,CAAClB,EAAE;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAMR,CACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN5D,OAAA;MAAKuD,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BxD,OAAA;QAAKuD,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BxD,OAAA;UAAAwD,QAAA,EAAK,EAAAnD,kBAAA,GAAAyC,YAAY,CAACoB,IAAI,CAACH,IAAI,IAAIA,IAAI,CAAChB,EAAE,KAAKxC,aAAa,CAAC,cAAAF,kBAAA,uBAApDA,kBAAA,CAAsD2C,KAAK,KAAI;QAAW;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACrF5D,OAAA;UAAKuD,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxBxD,OAAA;YAAMuD,SAAS,EAAC,cAAc;YAAAC,QAAA,EAC3B,IAAIW,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,OAAO,EAAE;cACtCC,OAAO,EAAE,MAAM;cACfC,IAAI,EAAE,SAAS;cACfC,KAAK,EAAE,MAAM;cACbC,GAAG,EAAE;YACP,CAAC;UAAC;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN5D,OAAA;QAAKuD,SAAS,EAAC,cAAc;QAAAC,QAAA,GAC1BjD,aAAa,KAAK,UAAU,iBAAIP,OAAA,CAACyE,eAAe;UAAC5D,KAAK,EAAEA;QAAM;UAAA4C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACjErD,aAAa,KAAK,OAAO,iBAAIP,OAAA,CAAC0E,YAAY;UAAClD,KAAK,EAAEF,IAAI,CAACE,KAAM;UAACmD,SAAS,EAAE9C;QAAa;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACzFrD,aAAa,KAAK,QAAQ,iBAAIP,OAAA,CAAC4E,aAAa;UAACnD,MAAM,EAAEH,IAAI,CAACG,MAAO;UAACkD,SAAS,EAAE9C;QAAa;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAC7FrD,aAAa,KAAK,UAAU,iBAAIP,OAAA,CAAC6E,eAAe;UAACnD,QAAQ,EAAEJ,IAAI,CAACI,QAAS;UAACiD,SAAS,EAAE9C;QAAa;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACrGrD,aAAa,KAAK,UAAU,iBAAIP,OAAA,CAAC8E,eAAe;UAACnD,QAAQ,EAAEL,IAAI,CAACK,QAAS;UAACgD,SAAS,EAAE9C;QAAa;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACrGrD,aAAa,KAAK,SAAS,iBAAIP,OAAA,CAAC+E,cAAc;UAACnD,OAAO,EAAEN,IAAI,CAACM,OAAQ;UAAC+C,SAAS,EAAE9C;QAAa;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/F,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLnD,SAAS,iBACRT,OAAA,CAACgF,KAAK;MACJC,IAAI,EAAEtE,SAAU;MAChBuE,OAAO,EAAEA,CAAA,KAAMxE,YAAY,CAAC,KAAK,CAAE;MACnCyE,SAAS,EAAEA,CAAA,KAAM;QACfzE,YAAY,CAAC,KAAK,CAAC;QACnBmB,YAAY,CAAC,CAAC;MAChB;IAAE;MAAA4B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;;AAED;AAAAxD,EAAA,CA5MMD,SAAS;EAAA,QACIL,OAAO;AAAA;AAAAsF,EAAA,GADpBjF,SAAS;AA6Mf,MAAMsE,eAAe,GAAGA,CAAC;EAAE5D;AAAM,CAAC,kBAChCb,OAAA;EAAKuD,SAAS,EAAC,kBAAkB;EAAAC,QAAA,gBAC/BxD,OAAA;IAAKuD,SAAS,EAAC,YAAY;IAAAC,QAAA,gBACzBxD,OAAA;MAAKuD,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBxD,OAAA;QAAKuD,SAAS,EAAC,WAAW;QAAAC,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACnC5D,OAAA;QAAKuD,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBxD,OAAA;UAAAwD,QAAA,EAAK3C,KAAK,CAACE;QAAU;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC3B5D,OAAA;UAAAwD,QAAA,EAAG;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACN5D,OAAA;MAAKuD,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBxD,OAAA;QAAKuD,SAAS,EAAC,WAAW;QAAAC,QAAA,EAAC;MAAC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAClC5D,OAAA;QAAKuD,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBxD,OAAA;UAAAwD,QAAA,EAAK3C,KAAK,CAACG;QAAW;UAAAyC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC5B5D,OAAA;UAAAwD,QAAA,EAAG;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACN5D,OAAA;MAAKuD,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBxD,OAAA;QAAKuD,SAAS,EAAC,WAAW;QAAAC,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACnC5D,OAAA;QAAKuD,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBxD,OAAA;UAAAwD,QAAA,EAAK3C,KAAK,CAACI;QAAa;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC9B5D,OAAA;UAAAwD,QAAA,EAAG;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACN5D,OAAA;MAAKuD,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBxD,OAAA;QAAKuD,SAAS,EAAC,WAAW;QAAAC,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACnC5D,OAAA;QAAKuD,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBxD,OAAA;UAAAwD,QAAA,EAAK3C,KAAK,CAACK;QAAa;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC9B5D,OAAA;UAAAwD,QAAA,EAAG;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACN5D,OAAA;MAAKuD,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBxD,OAAA;QAAKuD,SAAS,EAAC,WAAW;QAAAC,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACnC5D,OAAA;QAAKuD,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBxD,OAAA;UAAAwD,QAAA,EAAK3C,KAAK,CAACM;QAAY;UAAAsC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC7B5D,OAAA;UAAAwD,QAAA,EAAG;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC,eACN5D,OAAA;IAAKuD,SAAS,EAAC,iBAAiB;IAAAC,QAAA,gBAC9BxD,OAAA;MAAAwD,QAAA,EAAI;IAAe;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACxB5D,OAAA;MAAAwD,QAAA,EAAG;IAAsD;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC1D,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACH,CACN;;AAED;AAAAyB,GAAA,GA9CMZ,eAAe;AA+CrB,MAAMC,YAAY,GAAGA,CAAC;EAAElD,KAAK;EAAEmD;AAAU,CAAC,kBACxC3E,OAAA;EAAKuD,SAAS,EAAC,iBAAiB;EAAAC,QAAA,EAC7BhC,KAAK,CAACe,MAAM,KAAK,CAAC,gBACjBvC,OAAA;IAAKuD,SAAS,EAAC,aAAa;IAAAC,QAAA,gBAC1BxD,OAAA;MAAKuD,SAAS,EAAC,YAAY;MAAAC,QAAA,EAAC;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eACpC5D,OAAA;MAAAwD,QAAA,EAAI;IAAY;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACrB5D,OAAA;MAAAwD,QAAA,EAAG;IAAsC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC1C,CAAC,gBAEN5D,OAAA;IAAKuD,SAAS,EAAC,cAAc;IAAAC,QAAA,EAC1BhC,KAAK,CAACsC,GAAG,CAAEwB,IAAI,iBACdtF,OAAA;MAAmBuD,SAAS,EAAC,cAAc;MAAAC,QAAA,gBACzCxD,OAAA;QAAAwD,QAAA,EAAK8B,IAAI,CAACC;MAAK;QAAA9B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACrB5D,OAAA;QAAAwD,QAAA,GAAI8B,IAAI,CAACE,OAAO,CAACC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,EAAC,KAAG;MAAA;QAAAhC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAC1C5D,OAAA;QAAKuD,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BxD,OAAA;UAAQuD,SAAS,EAAC,UAAU;UAAAC,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC1C5D,OAAA;UAAQuD,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC,eACN5D,OAAA;QAAKuD,SAAS,EAAC,WAAW;QAAAC,QAAA,EACvB,IAAIW,IAAI,CAACmB,IAAI,CAACI,SAAS,CAAC,CAACtB,kBAAkB,CAAC;MAAC;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC;IAAA,GATE0B,IAAI,CAACvC,EAAE;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAUZ,CACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC;AACN;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACE,CACN;;AAED;AAAA+B,GAAA,GA5BMjB,YAAY;AA6BlB,MAAME,aAAa,GAAGA,CAAC;EAAEnD,MAAM;EAAEkD;AAAU,CAAC,KAAK;EAC/C,MAAMiB,WAAW,GAAG,MAAOC,OAAO,IAAK;IACrC,IAAI;MACF,MAAM/D,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAM8D,QAAQ,GAAG,MAAM5D,KAAK,CAAC,gCAAgC2D,OAAO,SAAS,EAAE;QAC7EE,MAAM,EAAE,KAAK;QACb5D,OAAO,EAAE;UAAE,eAAe,EAAE,UAAUL,KAAK;QAAG;MAChD,CAAC,CAAC;MAEF,IAAIgE,QAAQ,CAAC1D,EAAE,EAAE;QACfuC,SAAS,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,MAAM;QACL9B,OAAO,CAACD,KAAK,CAAC,wBAAwB,CAAC;MACzC;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C;EACF,CAAC;EAED,oBACE5C,OAAA;IAAKuD,SAAS,EAAC,iBAAiB;IAAAC,QAAA,EAC7B/B,MAAM,CAACc,MAAM,KAAK,CAAC,gBAClBvC,OAAA;MAAKuD,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BxD,OAAA;QAAKuD,SAAS,EAAC,YAAY;QAAAC,QAAA,EAAC;MAAC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACnC5D,OAAA;QAAAwD,QAAA,EAAI;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACtB5D,OAAA;QAAAwD,QAAA,EAAG;MAAuC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3C,CAAC,gBAEN5D,OAAA;MAAKuD,SAAS,EAAC,aAAa;MAAAC,QAAA,EACzB/B,MAAM,CAACqC,GAAG,CAAEkC,KAAK,iBAChBhG,OAAA;QAAoBuD,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACxCxD,OAAA;UAAKuD,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBxD,OAAA;YAAAwD,QAAA,EAAKwC,KAAK,CAACC;UAAU;YAAAxC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC3B5D,OAAA;YAAAwD,QAAA,EAAG;UAAyB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eACN5D,OAAA;UAAKuD,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3BxD,OAAA;YACEiF,IAAI,EAAC,UAAU;YACfiB,OAAO,EAAEF,KAAK,CAACG,YAAa;YAC5BC,QAAQ,EAAEA,CAAA,KAAMR,WAAW,CAACI,KAAK,CAACjD,EAAE;UAAE;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA,GAXEoC,KAAK,CAACjD,EAAE;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAYb,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EACN;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;;AAED;AAAAyC,GAAA,GAlDMzB,aAAa;AAmDnB,MAAMC,eAAe,GAAGA,CAAC;EAAEnD,QAAQ;EAAEiD;AAAU,CAAC,kBAC9C3E,OAAA;EAAKuD,SAAS,EAAC,iBAAiB;EAAAC,QAAA,EAC7B9B,QAAQ,CAACa,MAAM,KAAK,CAAC,gBACpBvC,OAAA;IAAKuD,SAAS,EAAC,aAAa;IAAAC,QAAA,gBAC1BxD,OAAA;MAAKuD,SAAS,EAAC,YAAY;MAAAC,QAAA,EAAC;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eACpC5D,OAAA;MAAAwD,QAAA,EAAI;IAAkB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC3B5D,OAAA;MAAAwD,QAAA,EAAG;IAA2C;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC/C,CAAC,gBAEN5D,OAAA;IAAKuD,SAAS,EAAC,eAAe;IAAAC,QAAA,EAC3B9B,QAAQ,CAACoC,GAAG,CAAEwC,IAAI,iBACjBtG,OAAA;MAAmBuD,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC1CxD,OAAA;QAAKuD,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBxD,OAAA;UAAAwD,QAAA,EAAK8C,IAAI,CAACA;QAAI;UAAA7C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACpB5D,OAAA;UAAAwD,QAAA,EAAI,IAAIW,IAAI,CAACmC,IAAI,CAACC,cAAc,CAAC,CAACC,cAAc,CAAC;QAAC;UAAA/C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC,eACN5D,OAAA;QAAKuD,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC1BxD,OAAA;UAAMuD,SAAS,EAAE,UAAU+C,IAAI,CAACG,OAAO,GAAG,WAAW,GAAG,SAAS,EAAG;UAAAjD,QAAA,EACjE8C,IAAI,CAACG,OAAO,GAAG,WAAW,GAAG;QAAS;UAAAhD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA,GATE0C,IAAI,CAACvD,EAAE;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAUZ,CACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC;AACN;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACE,CACN;;AAED;AAAA8C,GAAA,GA5BM7B,eAAe;AA6BrB,MAAMC,eAAe,GAAGA,CAAC;EAAEnD,QAAQ;EAAEgD;AAAU,CAAC,kBAC9C3E,OAAA;EAAKuD,SAAS,EAAC,iBAAiB;EAAAC,QAAA,EAC7B7B,QAAQ,CAACY,MAAM,KAAK,CAAC,gBACpBvC,OAAA;IAAKuD,SAAS,EAAC,aAAa;IAAAC,QAAA,gBAC1BxD,OAAA;MAAKuD,SAAS,EAAC,YAAY;MAAAC,QAAA,EAAC;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eACpC5D,OAAA;MAAAwD,QAAA,EAAI;IAAe;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACxB5D,OAAA;MAAAwD,QAAA,EAAG;IAAkD;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACtD,CAAC,gBAEN5D,OAAA;IAAKuD,SAAS,EAAC,eAAe;IAAAC,QAAA,EAC3B7B,QAAQ,CAACmC,GAAG,CAAE6C,OAAO,iBACpB3G,OAAA;MAAsBuD,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC5CxD,OAAA;QAAAwD,QAAA,EAAKmD,OAAO,CAACC;MAAY;QAAAnD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC/B5D,OAAA;QAAAwD,QAAA,EAAG;MAAyB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA,GAFxB+C,OAAO,CAAC5D,EAAE;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAGf,CACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC;AACN;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACE,CACN;;AAED;AAAAiD,GAAA,GArBM/B,eAAe;AAsBrB,MAAMC,cAAc,GAAGA,CAAC;EAAEnD,OAAO;EAAE+C;AAAU,CAAC,kBAC5C3E,OAAA;EAAKuD,SAAS,EAAC,iBAAiB;EAAAC,QAAA,EAC7B5B,OAAO,CAACW,MAAM,KAAK,CAAC,gBACnBvC,OAAA;IAAKuD,SAAS,EAAC,aAAa;IAAAC,QAAA,gBAC1BxD,OAAA;MAAKuD,SAAS,EAAC,YAAY;MAAAC,QAAA,EAAC;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eACpC5D,OAAA;MAAAwD,QAAA,EAAI;IAAoB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC7B5D,OAAA;MAAAwD,QAAA,EAAG;IAAqC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACzC,CAAC,gBAEN5D,OAAA;IAAKuD,SAAS,EAAC,cAAc;IAAAC,QAAA,EAC1B5B,OAAO,CAACkC,GAAG,CAAEgD,MAAM,iBAClB9G,OAAA;MAAqBuD,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1CxD,OAAA;QAAAwD,QAAA,EAAKsD,MAAM,CAACvB;MAAK;QAAA9B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACvB5D,OAAA;QAAAwD,QAAA,GAAIsD,MAAM,CAACtB,OAAO,CAACC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,EAAC,KAAG;MAAA;QAAAhC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAC5C5D,OAAA;QAAKuD,SAAS,EAAC,aAAa;QAAAC,QAAA,EACzB,IAAIW,IAAI,CAAC2C,MAAM,CAACC,UAAU,CAAC,CAAC3C,kBAAkB,CAAC;MAAC;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C,CAAC;IAAA,GALEkD,MAAM,CAAC/D,EAAE;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAMd,CACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC;AACN;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACE,CACN;;AAED;AAAAoD,GAAA,GAxBMjC,cAAc;AAyBpB,MAAMC,KAAK,GAAGA,CAAC;EAAEC,IAAI;EAAEC,OAAO;EAAEC;AAAU,CAAC,KAAK;EAAA8B,GAAA;EAC9C,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGvH,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5C,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EAE7C,MAAMwH,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBjG,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF,MAAMS,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAIuF,GAAG,GAAG,EAAE;MACZ,IAAIC,IAAI,GAAG,CAAC,CAAC;MAEb,QAAQvC,IAAI;QACV,KAAK,MAAM;UACTsC,GAAG,GAAG,6BAA6B;UACnCC,IAAI,GAAG;YAAEjC,KAAK,EAAE2B,QAAQ,CAAC3B,KAAK;YAAEC,OAAO,EAAE0B,QAAQ,CAAC1B;UAAQ,CAAC;UAC3D;QACF,KAAK,OAAO;UACV+B,GAAG,GAAG,8BAA8B;UACpCC,IAAI,GAAG;YAAEvB,UAAU,EAAEiB,QAAQ,CAACrD;UAAK,CAAC;UACpC;QACF,KAAK,MAAM;UACT0D,GAAG,GAAG,gCAAgC;UACtCC,IAAI,GAAG;YAAElB,IAAI,EAAEY,QAAQ,CAACZ,IAAI;YAAEC,cAAc,EAAEW,QAAQ,CAACX;UAAe,CAAC;UACvE;QACF,KAAK,SAAS;UACZgB,GAAG,GAAG,gCAAgC;UACtCC,IAAI,GAAG;YAAEZ,YAAY,EAAEM,QAAQ,CAACN;UAAa,CAAC;UAC9C;QACF,KAAK,QAAQ;UACXW,GAAG,GAAG,+BAA+B;UACrCC,IAAI,GAAG;YAAEjC,KAAK,EAAE2B,QAAQ,CAAC3B,KAAK;YAAEC,OAAO,EAAE0B,QAAQ,CAAC1B;UAAQ,CAAC;UAC3D;QACF;UACE,MAAM,IAAIiC,KAAK,CAAC,cAAc,CAAC;MACnC;MAEA,MAAM3B,QAAQ,GAAG,MAAM5D,KAAK,CAACqF,GAAG,EAAE;QAChCxB,MAAM,EAAE,MAAM;QACd5D,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAE,UAAUL,KAAK;QAClC,CAAC;QACD0F,IAAI,EAAEE,IAAI,CAACC,SAAS,CAACH,IAAI;MAC3B,CAAC,CAAC;MAEF,IAAI1B,QAAQ,CAAC1D,EAAE,EAAE;QACf+C,SAAS,CAAC,CAAC;MACb,CAAC,MAAM;QACL,MAAMyC,SAAS,GAAG,MAAM9B,QAAQ,CAACzD,IAAI,CAAC,CAAC;QACvC,MAAM,IAAIoF,KAAK,CAACG,SAAS,CAACC,OAAO,IAAI,uBAAuB,CAAC;MAC/D;IACF,CAAC,CAAC,OAAOjF,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAAC;MAC9BkF,KAAK,CAAC,SAAS,GAAGlF,KAAK,CAACiF,OAAO,CAAC;IAClC,CAAC,SAAS;MACRxG,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM0G,UAAU,GAAGA,CAAA,KAAM;IACvB,QAAQ9C,IAAI;MACV,KAAK,MAAM;QACT,oBACEjF,OAAA,CAAAE,SAAA;UAAAsD,QAAA,gBACExD,OAAA;YACEiF,IAAI,EAAC,MAAM;YACX+C,WAAW,EAAC,YAAY;YACxBC,KAAK,EAAEf,QAAQ,CAAC3B,KAAK,IAAI,EAAG;YAC5Ba,QAAQ,EAAGiB,CAAC,IAAKF,WAAW,CAAC;cAAC,GAAGD,QAAQ;cAAE3B,KAAK,EAAE8B,CAAC,CAACa,MAAM,CAACD;YAAK,CAAC,CAAE;YACnEE,QAAQ;UAAA;YAAA1E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACF5D,OAAA;YACEgI,WAAW,EAAC,cAAc;YAC1BC,KAAK,EAAEf,QAAQ,CAAC1B,OAAO,IAAI,EAAG;YAC9BY,QAAQ,EAAGiB,CAAC,IAAKF,WAAW,CAAC;cAAC,GAAGD,QAAQ;cAAE1B,OAAO,EAAE6B,CAAC,CAACa,MAAM,CAACD;YAAK,CAAC,CAAE;YACrEG,IAAI,EAAC,GAAG;YACRD,QAAQ;UAAA;YAAA1E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA,eACF,CAAC;MAEP,KAAK,OAAO;QACV,oBACE5D,OAAA;UACEiF,IAAI,EAAC,MAAM;UACX+C,WAAW,EAAC,YAAY;UACxBC,KAAK,EAAEf,QAAQ,CAACrD,IAAI,IAAI,EAAG;UAC3BuC,QAAQ,EAAGiB,CAAC,IAAKF,WAAW,CAAC;YAAC,GAAGD,QAAQ;YAAErD,IAAI,EAAEwD,CAAC,CAACa,MAAM,CAACD;UAAK,CAAC,CAAE;UAClEE,QAAQ;QAAA;UAAA1E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAEN,KAAK,MAAM;QACT,oBACE5D,OAAA,CAAAE,SAAA;UAAAsD,QAAA,gBACExD,OAAA;YACEiF,IAAI,EAAC,MAAM;YACX+C,WAAW,EAAC,kBAAkB;YAC9BC,KAAK,EAAEf,QAAQ,CAACZ,IAAI,IAAI,EAAG;YAC3BF,QAAQ,EAAGiB,CAAC,IAAKF,WAAW,CAAC;cAAC,GAAGD,QAAQ;cAAEZ,IAAI,EAAEe,CAAC,CAACa,MAAM,CAACD;YAAK,CAAC,CAAE;YAClEE,QAAQ;UAAA;YAAA1E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACF5D,OAAA;YACEiF,IAAI,EAAC,gBAAgB;YACrBgD,KAAK,EAAEf,QAAQ,CAACX,cAAc,IAAI,EAAG;YACrCH,QAAQ,EAAGiB,CAAC,IAAKF,WAAW,CAAC;cAAC,GAAGD,QAAQ;cAAEX,cAAc,EAAEc,CAAC,CAACa,MAAM,CAACD;YAAK,CAAC,CAAE;YAC5EE,QAAQ;UAAA;YAAA1E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA,eACF,CAAC;MAEP,KAAK,SAAS;QACZ,oBACE5D,OAAA;UACEiF,IAAI,EAAC,MAAM;UACX+C,WAAW,EAAC,cAAc;UAC1BC,KAAK,EAAEf,QAAQ,CAACN,YAAY,IAAI,EAAG;UACnCR,QAAQ,EAAGiB,CAAC,IAAKF,WAAW,CAAC;YAAC,GAAGD,QAAQ;YAAEN,YAAY,EAAES,CAAC,CAACa,MAAM,CAACD;UAAK,CAAC,CAAE;UAC1EE,QAAQ;QAAA;UAAA1E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAEN,KAAK,QAAQ;QACX,oBACE5D,OAAA,CAAAE,SAAA;UAAAsD,QAAA,gBACExD,OAAA;YACEiF,IAAI,EAAC,MAAM;YACX+C,WAAW,EAAC,cAAc;YAC1BC,KAAK,EAAEf,QAAQ,CAAC3B,KAAK,IAAI,EAAG;YAC5Ba,QAAQ,EAAGiB,CAAC,IAAKF,WAAW,CAAC;cAAC,GAAGD,QAAQ;cAAE3B,KAAK,EAAE8B,CAAC,CAACa,MAAM,CAACD;YAAK,CAAC,CAAE;YACnEE,QAAQ;UAAA;YAAA1E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACF5D,OAAA;YACEgI,WAAW,EAAC,0BAA0B;YACtCC,KAAK,EAAEf,QAAQ,CAAC1B,OAAO,IAAI,EAAG;YAC9BY,QAAQ,EAAGiB,CAAC,IAAKF,WAAW,CAAC;cAAC,GAAGD,QAAQ;cAAE1B,OAAO,EAAE6B,CAAC,CAACa,MAAM,CAACD;YAAK,CAAC,CAAE;YACrEG,IAAI,EAAC,GAAG;YACRD,QAAQ;UAAA;YAAA1E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA,eACF,CAAC;MAEP;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,oBACE5D,OAAA;IAAKuD,SAAS,EAAC,eAAe;IAACS,OAAO,EAAEkB,OAAQ;IAAA1B,QAAA,eAC9CxD,OAAA;MAAKuD,SAAS,EAAC,eAAe;MAACS,OAAO,EAAGqD,CAAC,IAAKA,CAAC,CAACgB,eAAe,CAAC,CAAE;MAAA7E,QAAA,gBACjExD,OAAA;QAAKuD,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BxD,OAAA;UAAAwD,QAAA,GAAI,SAAO,EAACyB,IAAI,CAACqD,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGtD,IAAI,CAACuD,KAAK,CAAC,CAAC,CAAC;QAAA;UAAA/E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC9D5D,OAAA;UAAQuD,SAAS,EAAC,aAAa;UAACS,OAAO,EAAEkB,OAAQ;UAAA1B,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD,CAAC,eACN5D,OAAA;QAAMyI,QAAQ,EAAErB,YAAa;QAAC7D,SAAS,EAAC,YAAY;QAAAC,QAAA,GACjDuE,UAAU,CAAC,CAAC,eACb/H,OAAA;UAAKuD,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BxD,OAAA;YAAQiF,IAAI,EAAC,QAAQ;YAACjB,OAAO,EAAEkB,OAAQ;YAAC3B,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAE/D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT5D,OAAA;YAAQiF,IAAI,EAAC,QAAQ;YAACyD,QAAQ,EAAEtH,OAAQ;YAACmC,SAAS,EAAC,YAAY;YAAAC,QAAA,EAC5DpC,OAAO,GAAG,aAAa,GAAG;UAAQ;YAAAqC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACqD,GAAA,CArKIjC,KAAK;AAAA2D,GAAA,GAAL3D,KAAK;AAuKX,eAAe7E,SAAS;AAAC,IAAAiF,EAAA,EAAAC,GAAA,EAAAM,GAAA,EAAAU,GAAA,EAAAK,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAA2B,GAAA;AAAAC,YAAA,CAAAxD,EAAA;AAAAwD,YAAA,CAAAvD,GAAA;AAAAuD,YAAA,CAAAjD,GAAA;AAAAiD,YAAA,CAAAvC,GAAA;AAAAuC,YAAA,CAAAlC,GAAA;AAAAkC,YAAA,CAAA/B,GAAA;AAAA+B,YAAA,CAAA5B,GAAA;AAAA4B,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}