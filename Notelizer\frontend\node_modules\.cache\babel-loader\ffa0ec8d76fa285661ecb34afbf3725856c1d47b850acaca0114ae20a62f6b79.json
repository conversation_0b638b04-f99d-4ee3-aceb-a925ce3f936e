{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\Notelizer\\\\frontend\\\\src\\\\pages\\\\Dashboard.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useAuth } from '../context/AuthContext';\nimport './Dashboard.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  var _sidebarItems$find;\n  const {\n    user\n  } = useAuth();\n  const [activeSection, setActiveSection] = useState('overview');\n  const [showModal, setShowModal] = useState(false);\n  const [modalType, setModalType] = useState('');\n  const [stats, setStats] = useState({\n    notesCount: 0,\n    habitsCount: 0,\n    scheduleCount: 0,\n    sectionsCount: 0,\n    reviewsCount: 0\n  });\n  const [loading, setLoading] = useState(true);\n  const [data, setData] = useState({\n    notes: [],\n    habits: [],\n    schedule: [],\n    sections: [],\n    reviews: []\n  });\n  useEffect(() => {\n    fetchAllData();\n  }, []);\n  const fetchAllData = async () => {\n    try {\n      const token = localStorage.getItem('token');\n\n      // Fetch notes\n      const notesResponse = await fetch('http://localhost:5000/notes', {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      if (notesResponse.ok) {\n        const notes = await notesResponse.json();\n        setData(prev => ({\n          ...prev,\n          notes\n        }));\n        setStats(prev => ({\n          ...prev,\n          notesCount: notes.length\n        }));\n      }\n\n      // TODO: Add other API calls when backend routes are ready\n    } catch (error) {\n      console.error('Error fetching data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const sidebarItems = [{\n    id: 'overview',\n    label: 'Overview',\n    icon: '📊'\n  }, {\n    id: 'notes',\n    label: 'My Notes',\n    icon: '📝'\n  }, {\n    id: 'habits',\n    label: 'My Habits',\n    icon: '✅'\n  }, {\n    id: 'schedule',\n    label: 'My Schedule',\n    icon: '📅'\n  }, {\n    id: 'sections',\n    label: 'My Sections',\n    icon: '📚'\n  }, {\n    id: 'reviews',\n    label: 'Daily Reviews',\n    icon: '📖'\n  }];\n  const quickActions = [{\n    id: 'note',\n    label: 'Create Note',\n    icon: '📝'\n  }, {\n    id: 'habit',\n    label: 'Add Habit',\n    icon: '✅'\n  }, {\n    id: 'task',\n    label: 'Schedule Task',\n    icon: '📅'\n  }, {\n    id: 'section',\n    label: 'Add Section',\n    icon: '📚'\n  }, {\n    id: 'review',\n    label: 'Daily Review',\n    icon: '📖'\n  }];\n  const handleQuickAction = actionType => {\n    setModalType(actionType);\n    setShowModal(true);\n  };\n  const handleSidebarClick = sectionId => {\n    setActiveSection(sectionId);\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-loading\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-spinner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Loading your dashboard...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"dashboard-layout\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-sidebar\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"sidebar-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Notelizer\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"Welcome, \", (user === null || user === void 0 ? void 0 : user.name) || 'User', \"!\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"sidebar-nav\",\n        children: sidebarItems.map(item => /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `sidebar-item ${activeSection === item.id ? 'active' : ''}`,\n          onClick: () => handleSidebarClick(item.id),\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"sidebar-icon\",\n            children: item.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"sidebar-label\",\n            children: item.label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 15\n          }, this)]\n        }, item.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"sidebar-quick-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          children: \"Quick Actions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this), quickActions.map(action => /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"quick-action-item\",\n          onClick: () => handleQuickAction(action.id),\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"action-icon\",\n            children: action.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"action-label\",\n            children: action.label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 15\n          }, this)]\n        }, action.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 13\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-main\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"main-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: ((_sidebarItems$find = sidebarItems.find(item => item.id === activeSection)) === null || _sidebarItems$find === void 0 ? void 0 : _sidebarItems$find.label) || 'Dashboard'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"date-info\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"current-date\",\n            children: new Date().toLocaleDateString('en-US', {\n              weekday: 'long',\n              year: 'numeric',\n              month: 'long',\n              day: 'numeric'\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"main-content\",\n        children: [activeSection === 'overview' && /*#__PURE__*/_jsxDEV(OverviewSection, {\n          stats: stats\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 44\n        }, this), activeSection === 'notes' && /*#__PURE__*/_jsxDEV(NotesSection, {\n          notes: data.notes,\n          onRefresh: fetchAllData\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 41\n        }, this), activeSection === 'habits' && /*#__PURE__*/_jsxDEV(HabitsSection, {\n          habits: data.habits,\n          onRefresh: fetchAllData\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 42\n        }, this), activeSection === 'schedule' && /*#__PURE__*/_jsxDEV(ScheduleSection, {\n          schedule: data.schedule,\n          onRefresh: fetchAllData\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 44\n        }, this), activeSection === 'sections' && /*#__PURE__*/_jsxDEV(SectionsSection, {\n          sections: data.sections,\n          onRefresh: fetchAllData\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 44\n        }, this), activeSection === 'reviews' && /*#__PURE__*/_jsxDEV(ReviewsSection, {\n          reviews: data.reviews,\n          onRefresh: fetchAllData\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 43\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 7\n    }, this), showModal && /*#__PURE__*/_jsxDEV(Modal, {\n      type: modalType,\n      onClose: () => setShowModal(false),\n      onSuccess: () => {\n        setShowModal(false);\n        fetchAllData();\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 154,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 90,\n    columnNumber: 5\n  }, this);\n};\n\n// Overview Section Component\n_s(Dashboard, \"R+Fm9qONJmqKHmhN6adhds3Nf8E=\", false, function () {\n  return [useAuth];\n});\n_c = Dashboard;\nconst OverviewSection = ({\n  stats\n}) => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"overview-section\",\n  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"stats-grid\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stat-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-icon\",\n        children: \"\\uD83D\\uDCDD\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: stats.notesCount\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Notes\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 171,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stat-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-icon\",\n        children: \"\\u2705\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: stats.habitsCount\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Habits\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 178,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stat-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-icon\",\n        children: \"\\uD83D\\uDCC5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: stats.scheduleCount\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Tasks\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 185,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stat-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-icon\",\n        children: \"\\uD83D\\uDCDA\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: stats.sectionsCount\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Sections\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 192,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stat-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-icon\",\n        children: \"\\uD83D\\uDCD6\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: stats.reviewsCount\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Reviews\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 199,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 170,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"recent-activity\",\n    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n      children: \"Recent Activity\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 208,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: \"Your recent notes, habits, and tasks will appear here.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 209,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 207,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 169,\n  columnNumber: 3\n}, this);\n\n// Notes Section Component\n_c2 = OverviewSection;\nconst NotesSection = ({\n  notes,\n  onRefresh\n}) => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"content-section\",\n  children: notes.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"empty-state\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"empty-icon\",\n      children: \"\\uD83D\\uDCDD\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 219,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n      children: \"No notes yet\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 220,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: \"Create your first note to get started!\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 221,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 218,\n    columnNumber: 7\n  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"content-grid\",\n    children: notes.map(note => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"content-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        children: note.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [note.content.substring(0, 100), \"...\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"edit-btn\",\n          children: \"Edit\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"delete-btn\",\n          children: \"Delete\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-date\",\n        children: new Date(note.timestamp).toLocaleDateString()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 233,\n        columnNumber: 13\n      }, this)]\n    }, note.id, true, {\n      fileName: _jsxFileName,\n      lineNumber: 226,\n      columnNumber: 11\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 224,\n    columnNumber: 7\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 216,\n  columnNumber: 3\n}, this);\n\n// Habits Section Component\n_c3 = NotesSection;\nconst HabitsSection = ({\n  habits,\n  onRefresh\n}) => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"content-section\",\n  children: habits.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"empty-state\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"empty-icon\",\n      children: \"\\u2705\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 248,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n      children: \"No habits yet\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 249,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: \"Add your first habit to start tracking!\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 250,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 247,\n    columnNumber: 7\n  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"habits-list\",\n    children: habits.map(habit => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"habit-item\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"habit-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          children: habit.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Track your daily progress\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"habit-toggle\",\n        children: /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"checkbox\",\n          checked: habit.is_completed,\n          onChange: () => {/* Toggle habit */}\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 260,\n        columnNumber: 13\n      }, this)]\n    }, habit.id, true, {\n      fileName: _jsxFileName,\n      lineNumber: 255,\n      columnNumber: 11\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 253,\n    columnNumber: 7\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 245,\n  columnNumber: 3\n}, this);\n\n// Schedule Section Component\n_c4 = HabitsSection;\nconst ScheduleSection = ({\n  schedule,\n  onRefresh\n}) => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"content-section\",\n  children: schedule.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"empty-state\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"empty-icon\",\n      children: \"\\uD83D\\uDCC5\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 279,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n      children: \"No scheduled tasks\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 280,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: \"Schedule your first task to stay organized!\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 281,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 278,\n    columnNumber: 7\n  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"schedule-list\",\n    children: schedule.map(task => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"schedule-item\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"task-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          children: task.task\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 288,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: new Date(task.scheduled_time).toLocaleString()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 287,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"task-status\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: `status ${task.is_done ? 'completed' : 'pending'}`,\n          children: task.is_done ? 'Completed' : 'Pending'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 291,\n        columnNumber: 13\n      }, this)]\n    }, task.id, true, {\n      fileName: _jsxFileName,\n      lineNumber: 286,\n      columnNumber: 11\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 284,\n    columnNumber: 7\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 276,\n  columnNumber: 3\n}, this);\n\n// Sections Section Component\n_c5 = ScheduleSection;\nconst SectionsSection = ({\n  sections,\n  onRefresh\n}) => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"content-section\",\n  children: sections.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"empty-state\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"empty-icon\",\n      children: \"\\uD83D\\uDCDA\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 308,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n      children: \"No sections yet\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 309,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: \"Create your first section to organize your topics!\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 310,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 307,\n    columnNumber: 7\n  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"sections-list\",\n    children: sections.map(section => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"section-item\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        children: section.section_name\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 316,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Click to view subsections\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 317,\n        columnNumber: 13\n      }, this)]\n    }, section.id, true, {\n      fileName: _jsxFileName,\n      lineNumber: 315,\n      columnNumber: 11\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 313,\n    columnNumber: 7\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 305,\n  columnNumber: 3\n}, this);\n\n// Reviews Section Component\n_c6 = SectionsSection;\nconst ReviewsSection = ({\n  reviews,\n  onRefresh\n}) => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"content-section\",\n  children: reviews.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"empty-state\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"empty-icon\",\n      children: \"\\uD83D\\uDCD6\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 330,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n      children: \"No daily reviews yet\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 331,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: \"Start writing your daily reflections!\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 332,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 329,\n    columnNumber: 7\n  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"reviews-list\",\n    children: reviews.map(review => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"review-item\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        children: review.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 338,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [review.content.substring(0, 150), \"...\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 339,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"review-date\",\n        children: new Date(review.created_at).toLocaleDateString()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 340,\n        columnNumber: 13\n      }, this)]\n    }, review.id, true, {\n      fileName: _jsxFileName,\n      lineNumber: 337,\n      columnNumber: 11\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 335,\n    columnNumber: 7\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 327,\n  columnNumber: 3\n}, this);\n\n// Modal Component\n_c7 = ReviewsSection;\nconst Modal = ({\n  type,\n  onClose,\n  onSuccess\n}) => {\n  _s2();\n  const [formData, setFormData] = useState({});\n  const [loading, setLoading] = useState(false);\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    try {\n      // TODO: Implement API calls based on type\n      console.log('Submitting:', type, formData);\n\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      onSuccess();\n    } catch (error) {\n      console.error('Error:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const renderForm = () => {\n    switch (type) {\n      case 'note':\n        return /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Note title\",\n            value: formData.title || '',\n            onChange: e => setFormData({\n              ...formData,\n              title: e.target.value\n            }),\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 379,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            placeholder: \"Note content\",\n            value: formData.content || '',\n            onChange: e => setFormData({\n              ...formData,\n              content: e.target.value\n            }),\n            rows: \"4\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true);\n      case 'habit':\n        return /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          placeholder: \"Habit name\",\n          value: formData.name || '',\n          onChange: e => setFormData({\n            ...formData,\n            name: e.target.value\n          }),\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 397,\n          columnNumber: 11\n        }, this);\n      case 'task':\n        return /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Task description\",\n            value: formData.task || '',\n            onChange: e => setFormData({\n              ...formData,\n              task: e.target.value\n            }),\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"datetime-local\",\n            value: formData.scheduled_time || '',\n            onChange: e => setFormData({\n              ...formData,\n              scheduled_time: e.target.value\n            }),\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 415,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true);\n      case 'section':\n        return /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          placeholder: \"Section name\",\n          value: formData.section_name || '',\n          onChange: e => setFormData({\n            ...formData,\n            section_name: e.target.value\n          }),\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 425,\n          columnNumber: 11\n        }, this);\n      case 'review':\n        return /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Review title\",\n            value: formData.title || '',\n            onChange: e => setFormData({\n              ...formData,\n              title: e.target.value\n            }),\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 436,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            placeholder: \"Your daily reflection...\",\n            value: formData.content || '',\n            onChange: e => setFormData({\n              ...formData,\n              content: e.target.value\n            }),\n            rows: \"4\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 443,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true);\n      default:\n        return null;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"modal-overlay\",\n    onClick: onClose,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-content\",\n      onClick: e => e.stopPropagation(),\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: [\"Create \", type.charAt(0).toUpperCase() + type.slice(1)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 461,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"modal-close\",\n          onClick: onClose,\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 462,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 460,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"modal-form\",\n        children: [renderForm(), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: onClose,\n            className: \"cancel-btn\",\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 467,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            disabled: loading,\n            className: \"submit-btn\",\n            children: loading ? 'Creating...' : 'Create'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 470,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 466,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 464,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 459,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 458,\n    columnNumber: 5\n  }, this);\n};\n_s2(Modal, \"aKbyWoXTTL1HyXbuBINaFJ9d+rc=\");\n_c8 = Modal;\nexport default Dashboard;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8;\n$RefreshReg$(_c, \"Dashboard\");\n$RefreshReg$(_c2, \"OverviewSection\");\n$RefreshReg$(_c3, \"NotesSection\");\n$RefreshReg$(_c4, \"HabitsSection\");\n$RefreshReg$(_c5, \"ScheduleSection\");\n$RefreshReg$(_c6, \"SectionsSection\");\n$RefreshReg$(_c7, \"ReviewsSection\");\n$RefreshReg$(_c8, \"Modal\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Dashboard", "_s", "_sidebarItems$find", "user", "activeSection", "setActiveSection", "showModal", "setShowModal", "modalType", "setModalType", "stats", "setStats", "notesCount", "habitsCount", "scheduleCount", "sectionsCount", "reviewsCount", "loading", "setLoading", "data", "setData", "notes", "habits", "schedule", "sections", "reviews", "fetchAllData", "token", "localStorage", "getItem", "notesResponse", "fetch", "headers", "ok", "json", "prev", "length", "error", "console", "sidebarItems", "id", "label", "icon", "quickActions", "handleQuickAction", "actionType", "handleSidebarClick", "sectionId", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "map", "item", "onClick", "action", "find", "Date", "toLocaleDateString", "weekday", "year", "month", "day", "OverviewSection", "NotesSection", "onRefresh", "HabitsSection", "ScheduleSection", "SectionsSection", "ReviewsSection", "Modal", "type", "onClose", "onSuccess", "_c", "_c2", "note", "title", "content", "substring", "timestamp", "_c3", "habit", "checked", "is_completed", "onChange", "_c4", "task", "scheduled_time", "toLocaleString", "is_done", "_c5", "section", "section_name", "_c6", "review", "created_at", "_c7", "_s2", "formData", "setFormData", "handleSubmit", "e", "preventDefault", "log", "Promise", "resolve", "setTimeout", "renderForm", "placeholder", "value", "target", "required", "rows", "stopPropagation", "char<PERSON>t", "toUpperCase", "slice", "onSubmit", "disabled", "_c8", "$RefreshReg$"], "sources": ["D:/Projects/Notelizer/frontend/src/pages/Dashboard.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useAuth } from '../context/AuthContext';\nimport './Dashboard.css';\n\nconst Dashboard = () => {\n  const { user } = useAuth();\n  const [activeSection, setActiveSection] = useState('overview');\n  const [showModal, setShowModal] = useState(false);\n  const [modalType, setModalType] = useState('');\n  const [stats, setStats] = useState({\n    notesCount: 0,\n    habitsCount: 0,\n    scheduleCount: 0,\n    sectionsCount: 0,\n    reviewsCount: 0\n  });\n  const [loading, setLoading] = useState(true);\n  const [data, setData] = useState({\n    notes: [],\n    habits: [],\n    schedule: [],\n    sections: [],\n    reviews: []\n  });\n\n  useEffect(() => {\n    fetchAllData();\n  }, []);\n\n  const fetchAllData = async () => {\n    try {\n      const token = localStorage.getItem('token');\n\n      // Fetch notes\n      const notesResponse = await fetch('http://localhost:5000/notes', {\n        headers: { 'Authorization': `Bearer ${token}` }\n      });\n\n      if (notesResponse.ok) {\n        const notes = await notesResponse.json();\n        setData(prev => ({ ...prev, notes }));\n        setStats(prev => ({ ...prev, notesCount: notes.length }));\n      }\n\n      // TODO: Add other API calls when backend routes are ready\n\n    } catch (error) {\n      console.error('Error fetching data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const sidebarItems = [\n    { id: 'overview', label: 'Overview', icon: '📊' },\n    { id: 'notes', label: 'My Notes', icon: '📝' },\n    { id: 'habits', label: 'My Habits', icon: '✅' },\n    { id: 'schedule', label: 'My Schedule', icon: '📅' },\n    { id: 'sections', label: 'My Sections', icon: '📚' },\n    { id: 'reviews', label: 'Daily Reviews', icon: '📖' }\n  ];\n\n  const quickActions = [\n    { id: 'note', label: 'Create Note', icon: '📝' },\n    { id: 'habit', label: 'Add Habit', icon: '✅' },\n    { id: 'task', label: 'Schedule Task', icon: '📅' },\n    { id: 'section', label: 'Add Section', icon: '📚' },\n    { id: 'review', label: 'Daily Review', icon: '📖' }\n  ];\n\n  const handleQuickAction = (actionType) => {\n    setModalType(actionType);\n    setShowModal(true);\n  };\n\n  const handleSidebarClick = (sectionId) => {\n    setActiveSection(sectionId);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"dashboard-loading\">\n        <div className=\"loading-spinner\"></div>\n        <p>Loading your dashboard...</p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"dashboard-layout\">\n      {/* Sidebar */}\n      <div className=\"dashboard-sidebar\">\n        <div className=\"sidebar-header\">\n          <h2>Notelizer</h2>\n          <p>Welcome, {user?.name || 'User'}!</p>\n        </div>\n\n        <div className=\"sidebar-nav\">\n          {sidebarItems.map((item) => (\n            <button\n              key={item.id}\n              className={`sidebar-item ${activeSection === item.id ? 'active' : ''}`}\n              onClick={() => handleSidebarClick(item.id)}\n            >\n              <span className=\"sidebar-icon\">{item.icon}</span>\n              <span className=\"sidebar-label\">{item.label}</span>\n            </button>\n          ))}\n        </div>\n\n        <div className=\"sidebar-quick-actions\">\n          <h4>Quick Actions</h4>\n          {quickActions.map((action) => (\n            <button\n              key={action.id}\n              className=\"quick-action-item\"\n              onClick={() => handleQuickAction(action.id)}\n            >\n              <span className=\"action-icon\">{action.icon}</span>\n              <span className=\"action-label\">{action.label}</span>\n            </button>\n          ))}\n        </div>\n      </div>\n\n      {/* Main Content */}\n      <div className=\"dashboard-main\">\n        <div className=\"main-header\">\n          <h1>{sidebarItems.find(item => item.id === activeSection)?.label || 'Dashboard'}</h1>\n          <div className=\"date-info\">\n            <span className=\"current-date\">\n              {new Date().toLocaleDateString('en-US', {\n                weekday: 'long',\n                year: 'numeric',\n                month: 'long',\n                day: 'numeric'\n              })}\n            </span>\n          </div>\n        </div>\n\n        <div className=\"main-content\">\n          {activeSection === 'overview' && <OverviewSection stats={stats} />}\n          {activeSection === 'notes' && <NotesSection notes={data.notes} onRefresh={fetchAllData} />}\n          {activeSection === 'habits' && <HabitsSection habits={data.habits} onRefresh={fetchAllData} />}\n          {activeSection === 'schedule' && <ScheduleSection schedule={data.schedule} onRefresh={fetchAllData} />}\n          {activeSection === 'sections' && <SectionsSection sections={data.sections} onRefresh={fetchAllData} />}\n          {activeSection === 'reviews' && <ReviewsSection reviews={data.reviews} onRefresh={fetchAllData} />}\n        </div>\n      </div>\n\n      {/* Modal for Quick Actions */}\n      {showModal && (\n        <Modal\n          type={modalType}\n          onClose={() => setShowModal(false)}\n          onSuccess={() => {\n            setShowModal(false);\n            fetchAllData();\n          }}\n        />\n      )}\n    </div>\n  );\n};\n\n// Overview Section Component\nconst OverviewSection = ({ stats }) => (\n  <div className=\"overview-section\">\n    <div className=\"stats-grid\">\n      <div className=\"stat-card\">\n        <div className=\"stat-icon\">📝</div>\n        <div className=\"stat-info\">\n          <h3>{stats.notesCount}</h3>\n          <p>Notes</p>\n        </div>\n      </div>\n      <div className=\"stat-card\">\n        <div className=\"stat-icon\">✅</div>\n        <div className=\"stat-info\">\n          <h3>{stats.habitsCount}</h3>\n          <p>Habits</p>\n        </div>\n      </div>\n      <div className=\"stat-card\">\n        <div className=\"stat-icon\">📅</div>\n        <div className=\"stat-info\">\n          <h3>{stats.scheduleCount}</h3>\n          <p>Tasks</p>\n        </div>\n      </div>\n      <div className=\"stat-card\">\n        <div className=\"stat-icon\">📚</div>\n        <div className=\"stat-info\">\n          <h3>{stats.sectionsCount}</h3>\n          <p>Sections</p>\n        </div>\n      </div>\n      <div className=\"stat-card\">\n        <div className=\"stat-icon\">📖</div>\n        <div className=\"stat-info\">\n          <h3>{stats.reviewsCount}</h3>\n          <p>Reviews</p>\n        </div>\n      </div>\n    </div>\n    <div className=\"recent-activity\">\n      <h3>Recent Activity</h3>\n      <p>Your recent notes, habits, and tasks will appear here.</p>\n    </div>\n  </div>\n);\n\n// Notes Section Component\nconst NotesSection = ({ notes, onRefresh }) => (\n  <div className=\"content-section\">\n    {notes.length === 0 ? (\n      <div className=\"empty-state\">\n        <div className=\"empty-icon\">📝</div>\n        <h3>No notes yet</h3>\n        <p>Create your first note to get started!</p>\n      </div>\n    ) : (\n      <div className=\"content-grid\">\n        {notes.map((note) => (\n          <div key={note.id} className=\"content-card\">\n            <h4>{note.title}</h4>\n            <p>{note.content.substring(0, 100)}...</p>\n            <div className=\"card-actions\">\n              <button className=\"edit-btn\">Edit</button>\n              <button className=\"delete-btn\">Delete</button>\n            </div>\n            <div className=\"card-date\">\n              {new Date(note.timestamp).toLocaleDateString()}\n            </div>\n          </div>\n        ))}\n      </div>\n    )}\n  </div>\n);\n\n// Habits Section Component\nconst HabitsSection = ({ habits, onRefresh }) => (\n  <div className=\"content-section\">\n    {habits.length === 0 ? (\n      <div className=\"empty-state\">\n        <div className=\"empty-icon\">✅</div>\n        <h3>No habits yet</h3>\n        <p>Add your first habit to start tracking!</p>\n      </div>\n    ) : (\n      <div className=\"habits-list\">\n        {habits.map((habit) => (\n          <div key={habit.id} className=\"habit-item\">\n            <div className=\"habit-info\">\n              <h4>{habit.name}</h4>\n              <p>Track your daily progress</p>\n            </div>\n            <div className=\"habit-toggle\">\n              <input\n                type=\"checkbox\"\n                checked={habit.is_completed}\n                onChange={() => {/* Toggle habit */}}\n              />\n            </div>\n          </div>\n        ))}\n      </div>\n    )}\n  </div>\n);\n\n// Schedule Section Component\nconst ScheduleSection = ({ schedule, onRefresh }) => (\n  <div className=\"content-section\">\n    {schedule.length === 0 ? (\n      <div className=\"empty-state\">\n        <div className=\"empty-icon\">📅</div>\n        <h3>No scheduled tasks</h3>\n        <p>Schedule your first task to stay organized!</p>\n      </div>\n    ) : (\n      <div className=\"schedule-list\">\n        {schedule.map((task) => (\n          <div key={task.id} className=\"schedule-item\">\n            <div className=\"task-info\">\n              <h4>{task.task}</h4>\n              <p>{new Date(task.scheduled_time).toLocaleString()}</p>\n            </div>\n            <div className=\"task-status\">\n              <span className={`status ${task.is_done ? 'completed' : 'pending'}`}>\n                {task.is_done ? 'Completed' : 'Pending'}\n              </span>\n            </div>\n          </div>\n        ))}\n      </div>\n    )}\n  </div>\n);\n\n// Sections Section Component\nconst SectionsSection = ({ sections, onRefresh }) => (\n  <div className=\"content-section\">\n    {sections.length === 0 ? (\n      <div className=\"empty-state\">\n        <div className=\"empty-icon\">📚</div>\n        <h3>No sections yet</h3>\n        <p>Create your first section to organize your topics!</p>\n      </div>\n    ) : (\n      <div className=\"sections-list\">\n        {sections.map((section) => (\n          <div key={section.id} className=\"section-item\">\n            <h4>{section.section_name}</h4>\n            <p>Click to view subsections</p>\n          </div>\n        ))}\n      </div>\n    )}\n  </div>\n);\n\n// Reviews Section Component\nconst ReviewsSection = ({ reviews, onRefresh }) => (\n  <div className=\"content-section\">\n    {reviews.length === 0 ? (\n      <div className=\"empty-state\">\n        <div className=\"empty-icon\">📖</div>\n        <h3>No daily reviews yet</h3>\n        <p>Start writing your daily reflections!</p>\n      </div>\n    ) : (\n      <div className=\"reviews-list\">\n        {reviews.map((review) => (\n          <div key={review.id} className=\"review-item\">\n            <h4>{review.title}</h4>\n            <p>{review.content.substring(0, 150)}...</p>\n            <div className=\"review-date\">\n              {new Date(review.created_at).toLocaleDateString()}\n            </div>\n          </div>\n        ))}\n      </div>\n    )}\n  </div>\n);\n\n// Modal Component\nconst Modal = ({ type, onClose, onSuccess }) => {\n  const [formData, setFormData] = useState({});\n  const [loading, setLoading] = useState(false);\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n\n    try {\n      // TODO: Implement API calls based on type\n      console.log('Submitting:', type, formData);\n\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 1000));\n\n      onSuccess();\n    } catch (error) {\n      console.error('Error:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const renderForm = () => {\n    switch (type) {\n      case 'note':\n        return (\n          <>\n            <input\n              type=\"text\"\n              placeholder=\"Note title\"\n              value={formData.title || ''}\n              onChange={(e) => setFormData({...formData, title: e.target.value})}\n              required\n            />\n            <textarea\n              placeholder=\"Note content\"\n              value={formData.content || ''}\n              onChange={(e) => setFormData({...formData, content: e.target.value})}\n              rows=\"4\"\n              required\n            />\n          </>\n        );\n      case 'habit':\n        return (\n          <input\n            type=\"text\"\n            placeholder=\"Habit name\"\n            value={formData.name || ''}\n            onChange={(e) => setFormData({...formData, name: e.target.value})}\n            required\n          />\n        );\n      case 'task':\n        return (\n          <>\n            <input\n              type=\"text\"\n              placeholder=\"Task description\"\n              value={formData.task || ''}\n              onChange={(e) => setFormData({...formData, task: e.target.value})}\n              required\n            />\n            <input\n              type=\"datetime-local\"\n              value={formData.scheduled_time || ''}\n              onChange={(e) => setFormData({...formData, scheduled_time: e.target.value})}\n              required\n            />\n          </>\n        );\n      case 'section':\n        return (\n          <input\n            type=\"text\"\n            placeholder=\"Section name\"\n            value={formData.section_name || ''}\n            onChange={(e) => setFormData({...formData, section_name: e.target.value})}\n            required\n          />\n        );\n      case 'review':\n        return (\n          <>\n            <input\n              type=\"text\"\n              placeholder=\"Review title\"\n              value={formData.title || ''}\n              onChange={(e) => setFormData({...formData, title: e.target.value})}\n              required\n            />\n            <textarea\n              placeholder=\"Your daily reflection...\"\n              value={formData.content || ''}\n              onChange={(e) => setFormData({...formData, content: e.target.value})}\n              rows=\"4\"\n              required\n            />\n          </>\n        );\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <div className=\"modal-overlay\" onClick={onClose}>\n      <div className=\"modal-content\" onClick={(e) => e.stopPropagation()}>\n        <div className=\"modal-header\">\n          <h3>Create {type.charAt(0).toUpperCase() + type.slice(1)}</h3>\n          <button className=\"modal-close\" onClick={onClose}>×</button>\n        </div>\n        <form onSubmit={handleSubmit} className=\"modal-form\">\n          {renderForm()}\n          <div className=\"modal-actions\">\n            <button type=\"button\" onClick={onClose} className=\"cancel-btn\">\n              Cancel\n            </button>\n            <button type=\"submit\" disabled={loading} className=\"submit-btn\">\n              {loading ? 'Creating...' : 'Create'}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAO,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEzB,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,kBAAA;EACtB,MAAM;IAAEC;EAAK,CAAC,GAAGR,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACS,aAAa,EAAEC,gBAAgB,CAAC,GAAGZ,QAAQ,CAAC,UAAU,CAAC;EAC9D,MAAM,CAACa,SAAS,EAAEC,YAAY,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACe,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACiB,KAAK,EAAEC,QAAQ,CAAC,GAAGlB,QAAQ,CAAC;IACjCmB,UAAU,EAAE,CAAC;IACbC,WAAW,EAAE,CAAC;IACdC,aAAa,EAAE,CAAC;IAChBC,aAAa,EAAE,CAAC;IAChBC,YAAY,EAAE;EAChB,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC0B,IAAI,EAAEC,OAAO,CAAC,GAAG3B,QAAQ,CAAC;IAC/B4B,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,OAAO,EAAE;EACX,CAAC,CAAC;EAEF/B,SAAS,CAAC,MAAM;IACdgC,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;;MAE3C;MACA,MAAMC,aAAa,GAAG,MAAMC,KAAK,CAAC,6BAA6B,EAAE;QAC/DC,OAAO,EAAE;UAAE,eAAe,EAAE,UAAUL,KAAK;QAAG;MAChD,CAAC,CAAC;MAEF,IAAIG,aAAa,CAACG,EAAE,EAAE;QACpB,MAAMZ,KAAK,GAAG,MAAMS,aAAa,CAACI,IAAI,CAAC,CAAC;QACxCd,OAAO,CAACe,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAEd;QAAM,CAAC,CAAC,CAAC;QACrCV,QAAQ,CAACwB,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAEvB,UAAU,EAAES,KAAK,CAACe;QAAO,CAAC,CAAC,CAAC;MAC3D;;MAEA;IAEF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC9C,CAAC,SAAS;MACRnB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMqB,YAAY,GAAG,CACnB;IAAEC,EAAE,EAAE,UAAU;IAAEC,KAAK,EAAE,UAAU;IAAEC,IAAI,EAAE;EAAK,CAAC,EACjD;IAAEF,EAAE,EAAE,OAAO;IAAEC,KAAK,EAAE,UAAU;IAAEC,IAAI,EAAE;EAAK,CAAC,EAC9C;IAAEF,EAAE,EAAE,QAAQ;IAAEC,KAAK,EAAE,WAAW;IAAEC,IAAI,EAAE;EAAI,CAAC,EAC/C;IAAEF,EAAE,EAAE,UAAU;IAAEC,KAAK,EAAE,aAAa;IAAEC,IAAI,EAAE;EAAK,CAAC,EACpD;IAAEF,EAAE,EAAE,UAAU;IAAEC,KAAK,EAAE,aAAa;IAAEC,IAAI,EAAE;EAAK,CAAC,EACpD;IAAEF,EAAE,EAAE,SAAS;IAAEC,KAAK,EAAE,eAAe;IAAEC,IAAI,EAAE;EAAK,CAAC,CACtD;EAED,MAAMC,YAAY,GAAG,CACnB;IAAEH,EAAE,EAAE,MAAM;IAAEC,KAAK,EAAE,aAAa;IAAEC,IAAI,EAAE;EAAK,CAAC,EAChD;IAAEF,EAAE,EAAE,OAAO;IAAEC,KAAK,EAAE,WAAW;IAAEC,IAAI,EAAE;EAAI,CAAC,EAC9C;IAAEF,EAAE,EAAE,MAAM;IAAEC,KAAK,EAAE,eAAe;IAAEC,IAAI,EAAE;EAAK,CAAC,EAClD;IAAEF,EAAE,EAAE,SAAS;IAAEC,KAAK,EAAE,aAAa;IAAEC,IAAI,EAAE;EAAK,CAAC,EACnD;IAAEF,EAAE,EAAE,QAAQ;IAAEC,KAAK,EAAE,cAAc;IAAEC,IAAI,EAAE;EAAK,CAAC,CACpD;EAED,MAAME,iBAAiB,GAAIC,UAAU,IAAK;IACxCpC,YAAY,CAACoC,UAAU,CAAC;IACxBtC,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMuC,kBAAkB,GAAIC,SAAS,IAAK;IACxC1C,gBAAgB,CAAC0C,SAAS,CAAC;EAC7B,CAAC;EAED,IAAI9B,OAAO,EAAE;IACX,oBACEpB,OAAA;MAAKmD,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCpD,OAAA;QAAKmD,SAAS,EAAC;MAAiB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACvCxD,OAAA;QAAAoD,QAAA,EAAG;MAAyB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B,CAAC;EAEV;EAEA,oBACExD,OAAA;IAAKmD,SAAS,EAAC,kBAAkB;IAAAC,QAAA,gBAE/BpD,OAAA;MAAKmD,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCpD,OAAA;QAAKmD,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BpD,OAAA;UAAAoD,QAAA,EAAI;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClBxD,OAAA;UAAAoD,QAAA,GAAG,WAAS,EAAC,CAAA9C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmD,IAAI,KAAI,MAAM,EAAC,GAAC;QAAA;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC,eAENxD,OAAA;QAAKmD,SAAS,EAAC,aAAa;QAAAC,QAAA,EACzBV,YAAY,CAACgB,GAAG,CAAEC,IAAI,iBACrB3D,OAAA;UAEEmD,SAAS,EAAE,gBAAgB5C,aAAa,KAAKoD,IAAI,CAAChB,EAAE,GAAG,QAAQ,GAAG,EAAE,EAAG;UACvEiB,OAAO,EAAEA,CAAA,KAAMX,kBAAkB,CAACU,IAAI,CAAChB,EAAE,CAAE;UAAAS,QAAA,gBAE3CpD,OAAA;YAAMmD,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAEO,IAAI,CAACd;UAAI;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACjDxD,OAAA;YAAMmD,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAEO,IAAI,CAACf;UAAK;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA,GAL9CG,IAAI,CAAChB,EAAE;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAMN,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENxD,OAAA;QAAKmD,SAAS,EAAC,uBAAuB;QAAAC,QAAA,gBACpCpD,OAAA;UAAAoD,QAAA,EAAI;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EACrBV,YAAY,CAACY,GAAG,CAAEG,MAAM,iBACvB7D,OAAA;UAEEmD,SAAS,EAAC,mBAAmB;UAC7BS,OAAO,EAAEA,CAAA,KAAMb,iBAAiB,CAACc,MAAM,CAAClB,EAAE,CAAE;UAAAS,QAAA,gBAE5CpD,OAAA;YAAMmD,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAES,MAAM,CAAChB;UAAI;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAClDxD,OAAA;YAAMmD,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAES,MAAM,CAACjB;UAAK;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA,GAL/CK,MAAM,CAAClB,EAAE;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAMR,CACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNxD,OAAA;MAAKmD,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BpD,OAAA;QAAKmD,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BpD,OAAA;UAAAoD,QAAA,EAAK,EAAA/C,kBAAA,GAAAqC,YAAY,CAACoB,IAAI,CAACH,IAAI,IAAIA,IAAI,CAAChB,EAAE,KAAKpC,aAAa,CAAC,cAAAF,kBAAA,uBAApDA,kBAAA,CAAsDuC,KAAK,KAAI;QAAW;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACrFxD,OAAA;UAAKmD,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxBpD,OAAA;YAAMmD,SAAS,EAAC,cAAc;YAAAC,QAAA,EAC3B,IAAIW,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,OAAO,EAAE;cACtCC,OAAO,EAAE,MAAM;cACfC,IAAI,EAAE,SAAS;cACfC,KAAK,EAAE,MAAM;cACbC,GAAG,EAAE;YACP,CAAC;UAAC;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENxD,OAAA;QAAKmD,SAAS,EAAC,cAAc;QAAAC,QAAA,GAC1B7C,aAAa,KAAK,UAAU,iBAAIP,OAAA,CAACqE,eAAe;UAACxD,KAAK,EAAEA;QAAM;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACjEjD,aAAa,KAAK,OAAO,iBAAIP,OAAA,CAACsE,YAAY;UAAC9C,KAAK,EAAEF,IAAI,CAACE,KAAM;UAAC+C,SAAS,EAAE1C;QAAa;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACzFjD,aAAa,KAAK,QAAQ,iBAAIP,OAAA,CAACwE,aAAa;UAAC/C,MAAM,EAAEH,IAAI,CAACG,MAAO;UAAC8C,SAAS,EAAE1C;QAAa;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAC7FjD,aAAa,KAAK,UAAU,iBAAIP,OAAA,CAACyE,eAAe;UAAC/C,QAAQ,EAAEJ,IAAI,CAACI,QAAS;UAAC6C,SAAS,EAAE1C;QAAa;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACrGjD,aAAa,KAAK,UAAU,iBAAIP,OAAA,CAAC0E,eAAe;UAAC/C,QAAQ,EAAEL,IAAI,CAACK,QAAS;UAAC4C,SAAS,EAAE1C;QAAa;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACrGjD,aAAa,KAAK,SAAS,iBAAIP,OAAA,CAAC2E,cAAc;UAAC/C,OAAO,EAAEN,IAAI,CAACM,OAAQ;UAAC2C,SAAS,EAAE1C;QAAa;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/F,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL/C,SAAS,iBACRT,OAAA,CAAC4E,KAAK;MACJC,IAAI,EAAElE,SAAU;MAChBmE,OAAO,EAAEA,CAAA,KAAMpE,YAAY,CAAC,KAAK,CAAE;MACnCqE,SAAS,EAAEA,CAAA,KAAM;QACfrE,YAAY,CAAC,KAAK,CAAC;QACnBmB,YAAY,CAAC,CAAC;MAChB;IAAE;MAAAwB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;;AAED;AAAApD,EAAA,CAlKMD,SAAS;EAAA,QACIL,OAAO;AAAA;AAAAkF,EAAA,GADpB7E,SAAS;AAmKf,MAAMkE,eAAe,GAAGA,CAAC;EAAExD;AAAM,CAAC,kBAChCb,OAAA;EAAKmD,SAAS,EAAC,kBAAkB;EAAAC,QAAA,gBAC/BpD,OAAA;IAAKmD,SAAS,EAAC,YAAY;IAAAC,QAAA,gBACzBpD,OAAA;MAAKmD,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBpD,OAAA;QAAKmD,SAAS,EAAC,WAAW;QAAAC,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACnCxD,OAAA;QAAKmD,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBpD,OAAA;UAAAoD,QAAA,EAAKvC,KAAK,CAACE;QAAU;UAAAsC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC3BxD,OAAA;UAAAoD,QAAA,EAAG;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNxD,OAAA;MAAKmD,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBpD,OAAA;QAAKmD,SAAS,EAAC,WAAW;QAAAC,QAAA,EAAC;MAAC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAClCxD,OAAA;QAAKmD,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBpD,OAAA;UAAAoD,QAAA,EAAKvC,KAAK,CAACG;QAAW;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC5BxD,OAAA;UAAAoD,QAAA,EAAG;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNxD,OAAA;MAAKmD,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBpD,OAAA;QAAKmD,SAAS,EAAC,WAAW;QAAAC,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACnCxD,OAAA;QAAKmD,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBpD,OAAA;UAAAoD,QAAA,EAAKvC,KAAK,CAACI;QAAa;UAAAoC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC9BxD,OAAA;UAAAoD,QAAA,EAAG;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNxD,OAAA;MAAKmD,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBpD,OAAA;QAAKmD,SAAS,EAAC,WAAW;QAAAC,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACnCxD,OAAA;QAAKmD,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBpD,OAAA;UAAAoD,QAAA,EAAKvC,KAAK,CAACK;QAAa;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC9BxD,OAAA;UAAAoD,QAAA,EAAG;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNxD,OAAA;MAAKmD,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBpD,OAAA;QAAKmD,SAAS,EAAC,WAAW;QAAAC,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACnCxD,OAAA;QAAKmD,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBpD,OAAA;UAAAoD,QAAA,EAAKvC,KAAK,CAACM;QAAY;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC7BxD,OAAA;UAAAoD,QAAA,EAAG;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC,eACNxD,OAAA;IAAKmD,SAAS,EAAC,iBAAiB;IAAAC,QAAA,gBAC9BpD,OAAA;MAAAoD,QAAA,EAAI;IAAe;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACxBxD,OAAA;MAAAoD,QAAA,EAAG;IAAsD;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC1D,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACH,CACN;;AAED;AAAAyB,GAAA,GA9CMZ,eAAe;AA+CrB,MAAMC,YAAY,GAAGA,CAAC;EAAE9C,KAAK;EAAE+C;AAAU,CAAC,kBACxCvE,OAAA;EAAKmD,SAAS,EAAC,iBAAiB;EAAAC,QAAA,EAC7B5B,KAAK,CAACe,MAAM,KAAK,CAAC,gBACjBvC,OAAA;IAAKmD,SAAS,EAAC,aAAa;IAAAC,QAAA,gBAC1BpD,OAAA;MAAKmD,SAAS,EAAC,YAAY;MAAAC,QAAA,EAAC;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eACpCxD,OAAA;MAAAoD,QAAA,EAAI;IAAY;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACrBxD,OAAA;MAAAoD,QAAA,EAAG;IAAsC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC1C,CAAC,gBAENxD,OAAA;IAAKmD,SAAS,EAAC,cAAc;IAAAC,QAAA,EAC1B5B,KAAK,CAACkC,GAAG,CAAEwB,IAAI,iBACdlF,OAAA;MAAmBmD,SAAS,EAAC,cAAc;MAAAC,QAAA,gBACzCpD,OAAA;QAAAoD,QAAA,EAAK8B,IAAI,CAACC;MAAK;QAAA9B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACrBxD,OAAA;QAAAoD,QAAA,GAAI8B,IAAI,CAACE,OAAO,CAACC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,EAAC,KAAG;MAAA;QAAAhC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAC1CxD,OAAA;QAAKmD,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BpD,OAAA;UAAQmD,SAAS,EAAC,UAAU;UAAAC,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC1CxD,OAAA;UAAQmD,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC,eACNxD,OAAA;QAAKmD,SAAS,EAAC,WAAW;QAAAC,QAAA,EACvB,IAAIW,IAAI,CAACmB,IAAI,CAACI,SAAS,CAAC,CAACtB,kBAAkB,CAAC;MAAC;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC;IAAA,GATE0B,IAAI,CAACvC,EAAE;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAUZ,CACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC;AACN;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACE,CACN;;AAED;AAAA+B,GAAA,GA5BMjB,YAAY;AA6BlB,MAAME,aAAa,GAAGA,CAAC;EAAE/C,MAAM;EAAE8C;AAAU,CAAC,kBAC1CvE,OAAA;EAAKmD,SAAS,EAAC,iBAAiB;EAAAC,QAAA,EAC7B3B,MAAM,CAACc,MAAM,KAAK,CAAC,gBAClBvC,OAAA;IAAKmD,SAAS,EAAC,aAAa;IAAAC,QAAA,gBAC1BpD,OAAA;MAAKmD,SAAS,EAAC,YAAY;MAAAC,QAAA,EAAC;IAAC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eACnCxD,OAAA;MAAAoD,QAAA,EAAI;IAAa;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACtBxD,OAAA;MAAAoD,QAAA,EAAG;IAAuC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC3C,CAAC,gBAENxD,OAAA;IAAKmD,SAAS,EAAC,aAAa;IAAAC,QAAA,EACzB3B,MAAM,CAACiC,GAAG,CAAE8B,KAAK,iBAChBxF,OAAA;MAAoBmD,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACxCpD,OAAA;QAAKmD,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBpD,OAAA;UAAAoD,QAAA,EAAKoC,KAAK,CAAC/B;QAAI;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACrBxD,OAAA;UAAAoD,QAAA,EAAG;QAAyB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC,eACNxD,OAAA;QAAKmD,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC3BpD,OAAA;UACE6E,IAAI,EAAC,UAAU;UACfY,OAAO,EAAED,KAAK,CAACE,YAAa;UAC5BC,QAAQ,EAAEA,CAAA,KAAM,CAAC;QAAoB;UAAAtC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA,GAXEgC,KAAK,CAAC7C,EAAE;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAYb,CACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC;AACN;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACE,CACN;;AAED;AAAAoC,GAAA,GA9BMpB,aAAa;AA+BnB,MAAMC,eAAe,GAAGA,CAAC;EAAE/C,QAAQ;EAAE6C;AAAU,CAAC,kBAC9CvE,OAAA;EAAKmD,SAAS,EAAC,iBAAiB;EAAAC,QAAA,EAC7B1B,QAAQ,CAACa,MAAM,KAAK,CAAC,gBACpBvC,OAAA;IAAKmD,SAAS,EAAC,aAAa;IAAAC,QAAA,gBAC1BpD,OAAA;MAAKmD,SAAS,EAAC,YAAY;MAAAC,QAAA,EAAC;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eACpCxD,OAAA;MAAAoD,QAAA,EAAI;IAAkB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC3BxD,OAAA;MAAAoD,QAAA,EAAG;IAA2C;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC/C,CAAC,gBAENxD,OAAA;IAAKmD,SAAS,EAAC,eAAe;IAAAC,QAAA,EAC3B1B,QAAQ,CAACgC,GAAG,CAAEmC,IAAI,iBACjB7F,OAAA;MAAmBmD,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC1CpD,OAAA;QAAKmD,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBpD,OAAA;UAAAoD,QAAA,EAAKyC,IAAI,CAACA;QAAI;UAAAxC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACpBxD,OAAA;UAAAoD,QAAA,EAAI,IAAIW,IAAI,CAAC8B,IAAI,CAACC,cAAc,CAAC,CAACC,cAAc,CAAC;QAAC;UAAA1C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC,eACNxD,OAAA;QAAKmD,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC1BpD,OAAA;UAAMmD,SAAS,EAAE,UAAU0C,IAAI,CAACG,OAAO,GAAG,WAAW,GAAG,SAAS,EAAG;UAAA5C,QAAA,EACjEyC,IAAI,CAACG,OAAO,GAAG,WAAW,GAAG;QAAS;UAAA3C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA,GATEqC,IAAI,CAAClD,EAAE;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAUZ,CACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC;AACN;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACE,CACN;;AAED;AAAAyC,GAAA,GA5BMxB,eAAe;AA6BrB,MAAMC,eAAe,GAAGA,CAAC;EAAE/C,QAAQ;EAAE4C;AAAU,CAAC,kBAC9CvE,OAAA;EAAKmD,SAAS,EAAC,iBAAiB;EAAAC,QAAA,EAC7BzB,QAAQ,CAACY,MAAM,KAAK,CAAC,gBACpBvC,OAAA;IAAKmD,SAAS,EAAC,aAAa;IAAAC,QAAA,gBAC1BpD,OAAA;MAAKmD,SAAS,EAAC,YAAY;MAAAC,QAAA,EAAC;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eACpCxD,OAAA;MAAAoD,QAAA,EAAI;IAAe;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACxBxD,OAAA;MAAAoD,QAAA,EAAG;IAAkD;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACtD,CAAC,gBAENxD,OAAA;IAAKmD,SAAS,EAAC,eAAe;IAAAC,QAAA,EAC3BzB,QAAQ,CAAC+B,GAAG,CAAEwC,OAAO,iBACpBlG,OAAA;MAAsBmD,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC5CpD,OAAA;QAAAoD,QAAA,EAAK8C,OAAO,CAACC;MAAY;QAAA9C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC/BxD,OAAA;QAAAoD,QAAA,EAAG;MAAyB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA,GAFxB0C,OAAO,CAACvD,EAAE;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAGf,CACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC;AACN;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACE,CACN;;AAED;AAAA4C,GAAA,GArBM1B,eAAe;AAsBrB,MAAMC,cAAc,GAAGA,CAAC;EAAE/C,OAAO;EAAE2C;AAAU,CAAC,kBAC5CvE,OAAA;EAAKmD,SAAS,EAAC,iBAAiB;EAAAC,QAAA,EAC7BxB,OAAO,CAACW,MAAM,KAAK,CAAC,gBACnBvC,OAAA;IAAKmD,SAAS,EAAC,aAAa;IAAAC,QAAA,gBAC1BpD,OAAA;MAAKmD,SAAS,EAAC,YAAY;MAAAC,QAAA,EAAC;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eACpCxD,OAAA;MAAAoD,QAAA,EAAI;IAAoB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC7BxD,OAAA;MAAAoD,QAAA,EAAG;IAAqC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACzC,CAAC,gBAENxD,OAAA;IAAKmD,SAAS,EAAC,cAAc;IAAAC,QAAA,EAC1BxB,OAAO,CAAC8B,GAAG,CAAE2C,MAAM,iBAClBrG,OAAA;MAAqBmD,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1CpD,OAAA;QAAAoD,QAAA,EAAKiD,MAAM,CAAClB;MAAK;QAAA9B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACvBxD,OAAA;QAAAoD,QAAA,GAAIiD,MAAM,CAACjB,OAAO,CAACC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,EAAC,KAAG;MAAA;QAAAhC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAC5CxD,OAAA;QAAKmD,SAAS,EAAC,aAAa;QAAAC,QAAA,EACzB,IAAIW,IAAI,CAACsC,MAAM,CAACC,UAAU,CAAC,CAACtC,kBAAkB,CAAC;MAAC;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C,CAAC;IAAA,GALE6C,MAAM,CAAC1D,EAAE;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAMd,CACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC;AACN;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACE,CACN;;AAED;AAAA+C,GAAA,GAxBM5B,cAAc;AAyBpB,MAAMC,KAAK,GAAGA,CAAC;EAAEC,IAAI;EAAEC,OAAO;EAAEC;AAAU,CAAC,KAAK;EAAAyB,GAAA;EAC9C,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG9G,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5C,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EAE7C,MAAM+G,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBxF,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF;MACAoB,OAAO,CAACqE,GAAG,CAAC,aAAa,EAAEjC,IAAI,EAAE4B,QAAQ,CAAC;;MAE1C;MACA,MAAM,IAAIM,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;MAEvDjC,SAAS,CAAC,CAAC;IACb,CAAC,CAAC,OAAOvC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAAC;IAChC,CAAC,SAAS;MACRnB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM6F,UAAU,GAAGA,CAAA,KAAM;IACvB,QAAQrC,IAAI;MACV,KAAK,MAAM;QACT,oBACE7E,OAAA,CAAAE,SAAA;UAAAkD,QAAA,gBACEpD,OAAA;YACE6E,IAAI,EAAC,MAAM;YACXsC,WAAW,EAAC,YAAY;YACxBC,KAAK,EAAEX,QAAQ,CAACtB,KAAK,IAAI,EAAG;YAC5BQ,QAAQ,EAAGiB,CAAC,IAAKF,WAAW,CAAC;cAAC,GAAGD,QAAQ;cAAEtB,KAAK,EAAEyB,CAAC,CAACS,MAAM,CAACD;YAAK,CAAC,CAAE;YACnEE,QAAQ;UAAA;YAAAjE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACFxD,OAAA;YACEmH,WAAW,EAAC,cAAc;YAC1BC,KAAK,EAAEX,QAAQ,CAACrB,OAAO,IAAI,EAAG;YAC9BO,QAAQ,EAAGiB,CAAC,IAAKF,WAAW,CAAC;cAAC,GAAGD,QAAQ;cAAErB,OAAO,EAAEwB,CAAC,CAACS,MAAM,CAACD;YAAK,CAAC,CAAE;YACrEG,IAAI,EAAC,GAAG;YACRD,QAAQ;UAAA;YAAAjE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA,eACF,CAAC;MAEP,KAAK,OAAO;QACV,oBACExD,OAAA;UACE6E,IAAI,EAAC,MAAM;UACXsC,WAAW,EAAC,YAAY;UACxBC,KAAK,EAAEX,QAAQ,CAAChD,IAAI,IAAI,EAAG;UAC3BkC,QAAQ,EAAGiB,CAAC,IAAKF,WAAW,CAAC;YAAC,GAAGD,QAAQ;YAAEhD,IAAI,EAAEmD,CAAC,CAACS,MAAM,CAACD;UAAK,CAAC,CAAE;UAClEE,QAAQ;QAAA;UAAAjE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAEN,KAAK,MAAM;QACT,oBACExD,OAAA,CAAAE,SAAA;UAAAkD,QAAA,gBACEpD,OAAA;YACE6E,IAAI,EAAC,MAAM;YACXsC,WAAW,EAAC,kBAAkB;YAC9BC,KAAK,EAAEX,QAAQ,CAACZ,IAAI,IAAI,EAAG;YAC3BF,QAAQ,EAAGiB,CAAC,IAAKF,WAAW,CAAC;cAAC,GAAGD,QAAQ;cAAEZ,IAAI,EAAEe,CAAC,CAACS,MAAM,CAACD;YAAK,CAAC,CAAE;YAClEE,QAAQ;UAAA;YAAAjE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACFxD,OAAA;YACE6E,IAAI,EAAC,gBAAgB;YACrBuC,KAAK,EAAEX,QAAQ,CAACX,cAAc,IAAI,EAAG;YACrCH,QAAQ,EAAGiB,CAAC,IAAKF,WAAW,CAAC;cAAC,GAAGD,QAAQ;cAAEX,cAAc,EAAEc,CAAC,CAACS,MAAM,CAACD;YAAK,CAAC,CAAE;YAC5EE,QAAQ;UAAA;YAAAjE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA,eACF,CAAC;MAEP,KAAK,SAAS;QACZ,oBACExD,OAAA;UACE6E,IAAI,EAAC,MAAM;UACXsC,WAAW,EAAC,cAAc;UAC1BC,KAAK,EAAEX,QAAQ,CAACN,YAAY,IAAI,EAAG;UACnCR,QAAQ,EAAGiB,CAAC,IAAKF,WAAW,CAAC;YAAC,GAAGD,QAAQ;YAAEN,YAAY,EAAES,CAAC,CAACS,MAAM,CAACD;UAAK,CAAC,CAAE;UAC1EE,QAAQ;QAAA;UAAAjE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAEN,KAAK,QAAQ;QACX,oBACExD,OAAA,CAAAE,SAAA;UAAAkD,QAAA,gBACEpD,OAAA;YACE6E,IAAI,EAAC,MAAM;YACXsC,WAAW,EAAC,cAAc;YAC1BC,KAAK,EAAEX,QAAQ,CAACtB,KAAK,IAAI,EAAG;YAC5BQ,QAAQ,EAAGiB,CAAC,IAAKF,WAAW,CAAC;cAAC,GAAGD,QAAQ;cAAEtB,KAAK,EAAEyB,CAAC,CAACS,MAAM,CAACD;YAAK,CAAC,CAAE;YACnEE,QAAQ;UAAA;YAAAjE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACFxD,OAAA;YACEmH,WAAW,EAAC,0BAA0B;YACtCC,KAAK,EAAEX,QAAQ,CAACrB,OAAO,IAAI,EAAG;YAC9BO,QAAQ,EAAGiB,CAAC,IAAKF,WAAW,CAAC;cAAC,GAAGD,QAAQ;cAAErB,OAAO,EAAEwB,CAAC,CAACS,MAAM,CAACD;YAAK,CAAC,CAAE;YACrEG,IAAI,EAAC,GAAG;YACRD,QAAQ;UAAA;YAAAjE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA,eACF,CAAC;MAEP;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,oBACExD,OAAA;IAAKmD,SAAS,EAAC,eAAe;IAACS,OAAO,EAAEkB,OAAQ;IAAA1B,QAAA,eAC9CpD,OAAA;MAAKmD,SAAS,EAAC,eAAe;MAACS,OAAO,EAAGgD,CAAC,IAAKA,CAAC,CAACY,eAAe,CAAC,CAAE;MAAApE,QAAA,gBACjEpD,OAAA;QAAKmD,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BpD,OAAA;UAAAoD,QAAA,GAAI,SAAO,EAACyB,IAAI,CAAC4C,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG7C,IAAI,CAAC8C,KAAK,CAAC,CAAC,CAAC;QAAA;UAAAtE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC9DxD,OAAA;UAAQmD,SAAS,EAAC,aAAa;UAACS,OAAO,EAAEkB,OAAQ;UAAA1B,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD,CAAC,eACNxD,OAAA;QAAM4H,QAAQ,EAAEjB,YAAa;QAACxD,SAAS,EAAC,YAAY;QAAAC,QAAA,GACjD8D,UAAU,CAAC,CAAC,eACblH,OAAA;UAAKmD,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BpD,OAAA;YAAQ6E,IAAI,EAAC,QAAQ;YAACjB,OAAO,EAAEkB,OAAQ;YAAC3B,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAE/D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTxD,OAAA;YAAQ6E,IAAI,EAAC,QAAQ;YAACgD,QAAQ,EAAEzG,OAAQ;YAAC+B,SAAS,EAAC,YAAY;YAAAC,QAAA,EAC5DhC,OAAO,GAAG,aAAa,GAAG;UAAQ;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACgD,GAAA,CA/HI5B,KAAK;AAAAkD,GAAA,GAALlD,KAAK;AAiIX,eAAezE,SAAS;AAAC,IAAA6E,EAAA,EAAAC,GAAA,EAAAM,GAAA,EAAAK,GAAA,EAAAK,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAuB,GAAA;AAAAC,YAAA,CAAA/C,EAAA;AAAA+C,YAAA,CAAA9C,GAAA;AAAA8C,YAAA,CAAAxC,GAAA;AAAAwC,YAAA,CAAAnC,GAAA;AAAAmC,YAAA,CAAA9B,GAAA;AAAA8B,YAAA,CAAA3B,GAAA;AAAA2B,YAAA,CAAAxB,GAAA;AAAAwB,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}