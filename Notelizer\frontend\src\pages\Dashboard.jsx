import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import './Dashboard.css';

const Dashboard = () => {
  const { user } = useAuth();
  const [activeSection, setActiveSection] = useState('overview');
  const [showModal, setShowModal] = useState(false);
  const [modalType, setModalType] = useState('');
  const [stats, setStats] = useState({
    notesCount: 0,
    habitsCount: 0,
    scheduleCount: 0,
    sectionsCount: 0,
    reviewsCount: 0
  });
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState({
    notes: [],
    habits: [],
    schedule: [],
    sections: [],
    reviews: []
  });

  useEffect(() => {
    fetchAllData();
  }, []);

  const fetchAllData = async () => {
    try {
      const token = localStorage.getItem('token');

      // Fetch notes
      const notesResponse = await fetch('http://localhost:5001/notes', {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      if (notesResponse.ok) {
        const notes = await notesResponse.json();
        setData(prev => ({ ...prev, notes }));
        setStats(prev => ({ ...prev, notesCount: notes.length }));
      }

      // Fetch habits
      const habitsResponse = await fetch('http://localhost:5001/habits', {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      if (habitsResponse.ok) {
        const habits = await habitsResponse.json();
        setData(prev => ({ ...prev, habits }));
        setStats(prev => ({ ...prev, habitsCount: habits.length }));
      }

      // Fetch schedule
      const scheduleResponse = await fetch('http://localhost:5001/schedule', {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      if (scheduleResponse.ok) {
        const schedule = await scheduleResponse.json();
        setData(prev => ({ ...prev, schedule }));
        setStats(prev => ({ ...prev, scheduleCount: schedule.length }));
      }

      // Fetch sections
      const sectionsResponse = await fetch('http://localhost:5001/sections', {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      if (sectionsResponse.ok) {
        const sections = await sectionsResponse.json();
        setData(prev => ({ ...prev, sections }));
        setStats(prev => ({ ...prev, sectionsCount: sections.length }));
      }

      // Fetch reviews
      const reviewsResponse = await fetch('http://localhost:5001/reviews', {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      if (reviewsResponse.ok) {
        const reviews = await reviewsResponse.json();
        setData(prev => ({ ...prev, reviews }));
        setStats(prev => ({ ...prev, reviewsCount: reviews.length }));
      }

    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setLoading(false);
    }
  };

  const sidebarItems = [
    { id: 'overview', label: 'Overview', icon: '📊' },
    { id: 'notes', label: 'My Notes', icon: '📝' },
    { id: 'habits', label: 'My Habits', icon: '✅' },
    { id: 'schedule', label: 'My Schedule', icon: '📅' },
    { id: 'sections', label: 'My Sections', icon: '📚' },
    { id: 'reviews', label: 'Daily Reviews', icon: '📖' }
  ];

  const quickActions = [
    { id: 'note', label: 'Create Note', icon: '📝' },
    { id: 'habit', label: 'Add Habit', icon: '✅' },
    { id: 'task', label: 'Schedule Task', icon: '📅' },
    { id: 'section', label: 'Add Section', icon: '📚' },
    { id: 'review', label: 'Daily Review', icon: '📖' }
  ];

  const handleQuickAction = (actionType) => {
    setModalType(actionType);
    setShowModal(true);
  };

  const handleSidebarClick = (sectionId) => {
    setActiveSection(sectionId);
  };

  if (loading) {
    return (
      <div className="dashboard-loading">
        <div className="loading-spinner"></div>
        <p>Loading your dashboard...</p>
      </div>
    );
  }

  return (
    <div className="dashboard-layout">
      {/* Sidebar */}
      <div className="dashboard-sidebar">
        <div className="sidebar-header">
          <h2>Notelizer</h2>
          <p>Welcome, {user?.name || 'User'}!</p>
        </div>

        <div className="sidebar-nav">
          {sidebarItems.map((item) => (
            <button
              key={item.id}
              className={`sidebar-item ${activeSection === item.id ? 'active' : ''}`}
              onClick={() => handleSidebarClick(item.id)}
            >
              <span className="sidebar-icon">{item.icon}</span>
              <span className="sidebar-label">{item.label}</span>
            </button>
          ))}
        </div>

        <div className="sidebar-quick-actions">
          <h4>Quick Actions</h4>
          {quickActions.map((action) => (
            <button
              key={action.id}
              className="quick-action-item"
              onClick={() => handleQuickAction(action.id)}
            >
              <span className="action-icon">{action.icon}</span>
              <span className="action-label">{action.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Main Content */}
      <div className="dashboard-main">
        <div className="main-header">
          <h1>{sidebarItems.find(item => item.id === activeSection)?.label || 'Dashboard'}</h1>
          <div className="date-info">
            <span className="current-date">
              {new Date().toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              })}
            </span>
          </div>
        </div>

        <div className="main-content">
          {activeSection === 'overview' && <OverviewSection stats={stats} />}
          {activeSection === 'notes' && <NotesSection notes={data.notes} onRefresh={fetchAllData} />}
          {activeSection === 'habits' && <HabitsSection habits={data.habits} onRefresh={fetchAllData} />}
          {activeSection === 'schedule' && <ScheduleSection schedule={data.schedule} onRefresh={fetchAllData} />}
          {activeSection === 'sections' && <SectionsSection sections={data.sections} onRefresh={fetchAllData} />}
          {activeSection === 'reviews' && <ReviewsSection reviews={data.reviews} onRefresh={fetchAllData} />}
        </div>
      </div>

      {/* Modal for Quick Actions */}
      {showModal && (
        <Modal
          type={modalType}
          onClose={() => setShowModal(false)}
          onSuccess={() => {
            setShowModal(false);
            fetchAllData();
          }}
        />
      )}
    </div>
  );
};

// Overview Section Component
const OverviewSection = ({ stats }) => (
  <div className="overview-section">
    <div className="stats-grid">
      <div className="stat-card">
        <div className="stat-icon">📝</div>
        <div className="stat-info">
          <h3>{stats.notesCount}</h3>
          <p>Notes</p>
        </div>
      </div>
      <div className="stat-card">
        <div className="stat-icon">✅</div>
        <div className="stat-info">
          <h3>{stats.habitsCount}</h3>
          <p>Habits</p>
        </div>
      </div>
      <div className="stat-card">
        <div className="stat-icon">📅</div>
        <div className="stat-info">
          <h3>{stats.scheduleCount}</h3>
          <p>Tasks</p>
        </div>
      </div>
      <div className="stat-card">
        <div className="stat-icon">📚</div>
        <div className="stat-info">
          <h3>{stats.sectionsCount}</h3>
          <p>Sections</p>
        </div>
      </div>
      <div className="stat-card">
        <div className="stat-icon">📖</div>
        <div className="stat-info">
          <h3>{stats.reviewsCount}</h3>
          <p>Reviews</p>
        </div>
      </div>
    </div>
    <div className="recent-activity">
      <h3>Recent Activity</h3>
      <p>Your recent notes, habits, and tasks will appear here.</p>
    </div>
  </div>
);

// Notes Section Component
const NotesSection = ({ notes, onRefresh }) => (
  <div className="content-section">
    {notes.length === 0 ? (
      <div className="empty-state">
        <div className="empty-icon">📝</div>
        <h3>No notes yet</h3>
        <p>Create your first note to get started!</p>
      </div>
    ) : (
      <div className="content-grid">
        {notes.map((note) => (
          <div key={note.id} className="content-card">
            <h4>{note.title}</h4>
            <p>{note.content.substring(0, 100)}...</p>
            <div className="card-actions">
              <button className="edit-btn">Edit</button>
              <button className="delete-btn">Delete</button>
            </div>
            <div className="card-date">
              {new Date(note.timestamp).toLocaleDateString()}
            </div>
          </div>
        ))}
      </div>
    )}
  </div>
);

// Habits Section Component
const HabitsSection = ({ habits, onRefresh }) => {
  const toggleHabit = async (habitId) => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`http://localhost:5001/habits/${habitId}/toggle`, {
        method: 'PUT',
        headers: { 'Authorization': `Bearer ${token}` }
      });

      if (response.ok) {
        onRefresh(); // Refresh the data
      } else {
        console.error('Failed to toggle habit');
      }
    } catch (error) {
      console.error('Error toggling habit:', error);
    }
  };

  return (
    <div className="content-section">
      {habits.length === 0 ? (
        <div className="empty-state">
          <div className="empty-icon">✅</div>
          <h3>No habits yet</h3>
          <p>Add your first habit to start tracking!</p>
        </div>
      ) : (
        <div className="habits-list">
          {habits.map((habit) => (
            <div key={habit.id} className="habit-item">
              <div className="habit-info">
                <h4>{habit.habit_name}</h4>
                <p>Track your daily progress</p>
              </div>
              <div className="habit-toggle">
                <input
                  type="checkbox"
                  checked={habit.is_completed}
                  onChange={() => toggleHabit(habit.id)}
                />
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

// Schedule Section Component
const ScheduleSection = ({ schedule, onRefresh }) => (
  <div className="content-section">
    {schedule.length === 0 ? (
      <div className="empty-state">
        <div className="empty-icon">📅</div>
        <h3>No scheduled tasks</h3>
        <p>Schedule your first task to stay organized!</p>
      </div>
    ) : (
      <div className="schedule-list">
        {schedule.map((task) => (
          <div key={task.id} className="schedule-item">
            <div className="task-info">
              <h4>{task.task}</h4>
              <p>{new Date(task.scheduled_time).toLocaleString()}</p>
            </div>
            <div className="task-status">
              <span className={`status ${task.is_done ? 'completed' : 'pending'}`}>
                {task.is_done ? 'Completed' : 'Pending'}
              </span>
            </div>
          </div>
        ))}
      </div>
    )}
  </div>
);

// Sections Section Component
const SectionsSection = ({ sections, onRefresh }) => (
  <div className="content-section">
    {sections.length === 0 ? (
      <div className="empty-state">
        <div className="empty-icon">📚</div>
        <h3>No sections yet</h3>
        <p>Create your first section to organize your topics!</p>
      </div>
    ) : (
      <div className="sections-list">
        {sections.map((section) => (
          <div key={section.id} className="section-item">
            <h4>{section.section_name}</h4>
            <p>Click to view subsections</p>
          </div>
        ))}
      </div>
    )}
  </div>
);

// Reviews Section Component
const ReviewsSection = ({ reviews, onRefresh }) => (
  <div className="content-section">
    {reviews.length === 0 ? (
      <div className="empty-state">
        <div className="empty-icon">📖</div>
        <h3>No daily reviews yet</h3>
        <p>Start writing your daily reflections!</p>
      </div>
    ) : (
      <div className="reviews-list">
        {reviews.map((review) => (
          <div key={review.id} className="review-item">
            <h4>{review.title}</h4>
            <p>{review.content.substring(0, 150)}...</p>
            <div className="review-date">
              {new Date(review.created_at).toLocaleDateString()}
            </div>
          </div>
        ))}
      </div>
    )}
  </div>
);

// Modal Component
const Modal = ({ type, onClose, onSuccess }) => {
  const [formData, setFormData] = useState({});
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      const token = localStorage.getItem('token');
      let url = '';
      let body = {};

      switch (type) {
        case 'note':
          url = 'http://localhost:5001/notes';
          body = { title: formData.title, content: formData.content };
          break;
        case 'habit':
          url = 'http://localhost:5001/habits';
          body = { habit_name: formData.name };
          break;
        case 'task':
          url = 'http://localhost:5001/schedule';
          body = { task: formData.task, scheduled_time: formData.scheduled_time };
          break;
        case 'section':
          url = 'http://localhost:5001/sections';
          body = { section_name: formData.section_name };
          break;
        case 'review':
          url = 'http://localhost:5001/reviews';
          body = { title: formData.title, content: formData.content };
          break;
        default:
          throw new Error('Unknown type');
      }

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(body)
      });

      if (response.ok) {
        onSuccess();
      } else {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to create item');
      }
    } catch (error) {
      console.error('Error:', error);
      alert('Error: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const renderForm = () => {
    switch (type) {
      case 'note':
        return (
          <>
            <input
              type="text"
              placeholder="Note title"
              value={formData.title || ''}
              onChange={(e) => setFormData({...formData, title: e.target.value})}
              required
            />
            <textarea
              placeholder="Note content"
              value={formData.content || ''}
              onChange={(e) => setFormData({...formData, content: e.target.value})}
              rows="4"
              required
            />
          </>
        );
      case 'habit':
        return (
          <input
            type="text"
            placeholder="Habit name"
            value={formData.name || ''}
            onChange={(e) => setFormData({...formData, name: e.target.value})}
            required
          />
        );
      case 'task':
        return (
          <>
            <input
              type="text"
              placeholder="Task description"
              value={formData.task || ''}
              onChange={(e) => setFormData({...formData, task: e.target.value})}
              required
            />
            <input
              type="datetime-local"
              value={formData.scheduled_time || ''}
              onChange={(e) => setFormData({...formData, scheduled_time: e.target.value})}
              required
            />
          </>
        );
      case 'section':
        return (
          <input
            type="text"
            placeholder="Section name"
            value={formData.section_name || ''}
            onChange={(e) => setFormData({...formData, section_name: e.target.value})}
            required
          />
        );
      case 'review':
        return (
          <>
            <input
              type="text"
              placeholder="Review title"
              value={formData.title || ''}
              onChange={(e) => setFormData({...formData, title: e.target.value})}
              required
            />
            <textarea
              placeholder="Your daily reflection..."
              value={formData.content || ''}
              onChange={(e) => setFormData({...formData, content: e.target.value})}
              rows="4"
              required
            />
          </>
        );
      default:
        return null;
    }
  };

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal-content" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h3>Create {type.charAt(0).toUpperCase() + type.slice(1)}</h3>
          <button className="modal-close" onClick={onClose}>×</button>
        </div>
        <form onSubmit={handleSubmit} className="modal-form">
          {renderForm()}
          <div className="modal-actions">
            <button type="button" onClick={onClose} className="cancel-btn">
              Cancel
            </button>
            <button type="submit" disabled={loading} className="submit-btn">
              {loading ? 'Creating...' : 'Create'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default Dashboard;
