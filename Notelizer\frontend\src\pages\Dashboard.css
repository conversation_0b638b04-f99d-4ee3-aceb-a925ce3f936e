.dashboard-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 30px 20px;
  min-height: calc(100vh - 80px);
  background: #87CEFA;
}

.dashboard-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 50vh;
  color: #666;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #4169E1;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.dashboard-header {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 30px;
  align-items: center;
  margin-bottom: 40px;
  padding: 30px;
  background: #4169E1;
  border-radius: 16px;
  color: white;
  box-shadow: 0 8px 32px rgba(65, 105, 225, 0.2);
}

.welcome-section h1.dashboard-title {
  font-size: 2.2rem;
  font-weight: 700;
  margin: 0 0 10px 0;
  line-height: 1.2;
}

.user-name {
  color: white;
  font-weight: 800;
}

.dashboard-subtitle {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  line-height: 1.5;
}

.dashboard-date {
  text-align: right;
}

.date-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 20px;
  min-width: 180px;
}

.date-day {
  font-size: 1.1rem;
  font-weight: 600;
  color: white;
  margin-bottom: 5px;
}

.date-full {
  font-size: 0.95rem;
  color: rgba(255, 255, 255, 0.8);
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 25px;
  margin-bottom: 50px;
}

.dashboard-card {
  background: #3E8EDE;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #2E7BCE;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.dashboard-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--card-color);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.dashboard-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.dashboard-card:hover::before {
  transform: scaleX(1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.card-icon {
  font-size: 2.5rem;
  line-height: 1;
}

.card-count {
  text-align: right;
}

.count-number {
  display: block;
  font-size: 2rem;
  font-weight: 800;
  color: white;
  line-height: 1;
}

.count-label {
  display: block;
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
  margin-top: 2px;
}

.card-content {
  margin-bottom: 20px;
}

.card-title {
  font-size: 1.4rem;
  font-weight: 700;
  color: white;
  margin: 0 0 8px 0;
}

.card-description {
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  line-height: 1.5;
}

.card-footer {
  display: flex;
  justify-content: flex-end;
}

.card-action {
  color: white;
  font-weight: 600;
  font-size: 0.95rem;
  display: flex;
  align-items: center;
  gap: 5px;
}

.arrow {
  transition: transform 0.3s ease;
}

.dashboard-card:hover .arrow {
  transform: translateX(3px);
}

.dashboard-quick-actions {
  background: #3E8EDE;
  border-radius: 16px;
  padding: 30px;
  border: 1px solid #2E7BCE;
}

.dashboard-quick-actions h3 {
  color: white;
  font-size: 1.3rem;
  font-weight: 700;
  margin: 0 0 20px 0;
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.quick-action-btn {
  background: white;
  border: 2px solid #2E7BCE;
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 1rem;
  font-weight: 600;
  color: #000;
}

.quick-action-btn:hover {
  border-color: #4169E1;
  background: #4169E1;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(65, 105, 225, 0.3);
}

.action-icon {
  font-size: 1.2rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-container {
    padding: 20px 15px;
  }
  
  .dashboard-header {
    grid-template-columns: 1fr;
    text-align: center;
    gap: 20px;
    padding: 25px 20px;
  }
  
  .dashboard-title {
    font-size: 1.8rem !important;
  }
  
  .dashboard-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .dashboard-card {
    padding: 25px 20px;
  }
  
  .quick-actions-grid {
    grid-template-columns: 1fr;
  }
  
  .date-card {
    min-width: auto;
  }
}
