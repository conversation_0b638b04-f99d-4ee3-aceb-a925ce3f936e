/* Dashboard Layout */
.dashboard-layout {
  display: flex;
  min-height: calc(100vh - 80px);
  background: #87CEFA;
}

.dashboard-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 50vh;
  color: #666;
  width: 100%;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #4169E1;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Sidebar Styles */
.dashboard-sidebar {
  width: 280px;
  background: #4169E1;
  color: white;
  padding: 20px;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
  position: fixed;
  height: calc(100vh - 80px);
  z-index: 100;
}

.sidebar-header {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.sidebar-header h2 {
  margin: 0 0 10px 0;
  font-size: 1.5rem;
  font-weight: 700;
}

.sidebar-header p {
  margin: 0;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
}

/* Sidebar Navigation */
.sidebar-nav {
  margin-bottom: 30px;
}

.sidebar-item {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 12px 16px;
  margin-bottom: 8px;
  background: transparent;
  border: none;
  border-radius: 8px;
  color: white;
  text-align: left;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.95rem;
}

.sidebar-item:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateX(4px);
}

.sidebar-item.active {
  background: rgba(255, 255, 255, 0.2);
  font-weight: 600;
}

.sidebar-icon {
  margin-right: 12px;
  font-size: 1.1rem;
}

.sidebar-label {
  flex: 1;
}

/* Quick Actions in Sidebar */
.sidebar-quick-actions {
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  padding-top: 20px;
}

.sidebar-quick-actions h4 {
  margin: 0 0 15px 0;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.quick-action-item {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 10px 16px;
  margin-bottom: 6px;
  background: transparent;
  border: none;
  border-radius: 6px;
  color: rgba(255, 255, 255, 0.9);
  text-align: left;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.85rem;
}

.quick-action-item:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.action-icon {
  margin-right: 10px;
  font-size: 0.9rem;
}

.action-label {
  flex: 1;
}

/* Main Content Area */
.dashboard-main {
  flex: 1;
  margin-left: 280px;
  padding: 30px;
  overflow-y: auto;
}

.main-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid rgba(255, 255, 255, 0.3);
}

.main-header h1 {
  margin: 0;
  color: #000;
  font-size: 2rem;
  font-weight: 700;
}

.date-info {
  color: #333;
  font-size: 0.95rem;
}

.current-date {
  font-weight: 500;
}

.main-content {
  min-height: 400px;
}

/* Overview Section */
.overview-section {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.stat-card {
  background: #3E8EDE;
  border-radius: 12px;
  padding: 25px;
  display: flex;
  align-items: center;
  gap: 15px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-3px);
}

.stat-icon {
  font-size: 2rem;
}

.stat-info h3 {
  margin: 0;
  font-size: 2rem;
  font-weight: 700;
  color: white;
}

.stat-info p {
  margin: 5px 0 0 0;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
}

.recent-activity {
  background: #3E8EDE;
  border-radius: 12px;
  padding: 25px;
  color: white;
}

.recent-activity h3 {
  margin: 0 0 15px 0;
  color: white;
}

.recent-activity p {
  margin: 0;
  color: rgba(255, 255, 255, 0.8);
}

/* Content Sections */
.content-section {
  width: 100%;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  background: #3E8EDE;
  border-radius: 12px;
  color: white;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 20px;
}

.empty-state h3 {
  margin: 0 0 10px 0;
  font-size: 1.5rem;
  color: white;
}

.empty-state p {
  margin: 0;
  color: rgba(255, 255, 255, 0.8);
  font-size: 1rem;
}

.content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.content-card {
  background: #3E8EDE;
  border-radius: 12px;
  padding: 20px;
  color: white;
  transition: transform 0.3s ease;
}

.content-card:hover {
  transform: translateY(-3px);
}

.content-card h4 {
  margin: 0 0 10px 0;
  color: white;
  font-size: 1.2rem;
}

.content-card p {
  margin: 0 0 15px 0;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.5;
}

.card-actions {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
}

.edit-btn, .delete-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.85rem;
  transition: background-color 0.3s;
}

.edit-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.edit-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.delete-btn {
  background: #dc3545;
  color: white;
}

.delete-btn:hover {
  background: #c82333;
}

.card-date {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.7);
  text-align: right;
}

/* Specific Content Types */
.habits-list, .schedule-list, .sections-list, .reviews-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.habit-item, .schedule-item, .section-item, .review-item {
  background: #3E8EDE;
  border-radius: 12px;
  padding: 20px;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: transform 0.3s ease;
}

.habit-item:hover, .schedule-item:hover, .section-item:hover, .review-item:hover {
  transform: translateY(-2px);
}

.habit-info h4, .task-info h4, .section-item h4, .review-item h4 {
  margin: 0 0 5px 0;
  color: white;
  font-size: 1.1rem;
}

.habit-info p, .task-info p, .section-item p, .review-item p {
  margin: 0;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
}

.habit-toggle input[type="checkbox"] {
  width: 20px;
  height: 20px;
  cursor: pointer;
}

.task-status .status {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.status.completed {
  background: #28a745;
  color: white;
}

.status.pending {
  background: #ffc107;
  color: #000;
}

.review-date {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.7);
  margin-top: 10px;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: #3E8EDE;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
  color: white;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.modal-header h3 {
  margin: 0;
  color: white;
  font-size: 1.3rem;
}

.modal-close {
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.3s;
}

.modal-close:hover {
  background: rgba(255, 255, 255, 0.1);
}

.modal-form {
  padding: 25px;
}

.modal-form input,
.modal-form textarea {
  width: 100%;
  padding: 12px;
  margin-bottom: 15px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 1rem;
  box-sizing: border-box;
}

.modal-form input::placeholder,
.modal-form textarea::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.modal-form input:focus,
.modal-form textarea:focus {
  outline: none;
  border-color: white;
  background: rgba(255, 255, 255, 0.15);
}

.modal-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  margin-top: 20px;
}

.cancel-btn, .submit-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.95rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.cancel-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.cancel-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.submit-btn {
  background: white;
  color: #3E8EDE;
}

.submit-btn:hover:not(:disabled) {
  background: #f0f0f0;
}

.submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-layout {
    flex-direction: column;
  }

  .dashboard-sidebar {
    position: relative;
    width: 100%;
    height: auto;
    padding: 15px;
  }

  .sidebar-header {
    text-align: center;
    margin-bottom: 20px;
  }

  .sidebar-nav {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
    margin-bottom: 20px;
  }

  .sidebar-item {
    flex: 0 0 auto;
    min-width: 120px;
    text-align: center;
    padding: 10px;
  }

  .sidebar-quick-actions {
    display: none;
  }

  .dashboard-main {
    margin-left: 0;
    padding: 20px 15px;
  }

  .main-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
  }

  .content-grid {
    grid-template-columns: 1fr;
  }

  .modal-content {
    width: 95%;
    margin: 20px;
  }

  .modal-form {
    padding: 20px;
  }

  .modal-actions {
    flex-direction: column;
  }

  .cancel-btn, .submit-btn {
    width: 100%;
  }
}
