{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\Notelizer\\\\frontend\\\\src\\\\pages\\\\Dashboard.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useAuth } from '../context/AuthContext';\nimport './Dashboard.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  var _sidebarItems$find;\n  const {\n    user\n  } = useAuth();\n  const [activeSection, setActiveSection] = useState('overview');\n  const [showModal, setShowModal] = useState(false);\n  const [modalType, setModalType] = useState('');\n  const [stats, setStats] = useState({\n    notesCount: 0,\n    habitsCount: 0,\n    scheduleCount: 0,\n    sectionsCount: 0,\n    reviewsCount: 0\n  });\n  const [loading, setLoading] = useState(true);\n  const [data, setData] = useState({\n    notes: [],\n    habits: [],\n    schedule: [],\n    sections: [],\n    reviews: []\n  });\n  useEffect(() => {\n    fetchAllData();\n  }, []);\n  const fetchAllData = async () => {\n    try {\n      const token = localStorage.getItem('token');\n\n      // Fetch notes\n      const notesResponse = await fetch('http://localhost:5000/notes', {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      if (notesResponse.ok) {\n        const notes = await notesResponse.json();\n        setData(prev => ({\n          ...prev,\n          notes\n        }));\n        setStats(prev => ({\n          ...prev,\n          notesCount: notes.length\n        }));\n      }\n\n      // TODO: Add other API calls when backend routes are ready\n    } catch (error) {\n      console.error('Error fetching data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const sidebarItems = [{\n    id: 'overview',\n    label: 'Overview',\n    icon: '📊'\n  }, {\n    id: 'notes',\n    label: 'My Notes',\n    icon: '📝'\n  }, {\n    id: 'habits',\n    label: 'My Habits',\n    icon: '✅'\n  }, {\n    id: 'schedule',\n    label: 'My Schedule',\n    icon: '📅'\n  }, {\n    id: 'sections',\n    label: 'My Sections',\n    icon: '📚'\n  }, {\n    id: 'reviews',\n    label: 'Daily Reviews',\n    icon: '📖'\n  }];\n  const quickActions = [{\n    id: 'note',\n    label: 'Create Note',\n    icon: '📝'\n  }, {\n    id: 'habit',\n    label: 'Add Habit',\n    icon: '✅'\n  }, {\n    id: 'task',\n    label: 'Schedule Task',\n    icon: '📅'\n  }, {\n    id: 'section',\n    label: 'Add Section',\n    icon: '📚'\n  }, {\n    id: 'review',\n    label: 'Daily Review',\n    icon: '📖'\n  }];\n  const handleQuickAction = actionType => {\n    setModalType(actionType);\n    setShowModal(true);\n  };\n  const handleSidebarClick = sectionId => {\n    setActiveSection(sectionId);\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-loading\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-spinner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Loading your dashboard...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"dashboard-layout\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-sidebar\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"sidebar-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Notelizer\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"Welcome, \", (user === null || user === void 0 ? void 0 : user.name) || 'User', \"!\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"sidebar-nav\",\n        children: sidebarItems.map(item => /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `sidebar-item ${activeSection === item.id ? 'active' : ''}`,\n          onClick: () => handleSidebarClick(item.id),\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"sidebar-icon\",\n            children: item.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"sidebar-label\",\n            children: item.label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 15\n          }, this)]\n        }, item.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"sidebar-quick-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          children: \"Quick Actions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this), quickActions.map(action => /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"quick-action-item\",\n          onClick: () => handleQuickAction(action.id),\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"action-icon\",\n            children: action.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"action-label\",\n            children: action.label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 15\n          }, this)]\n        }, action.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 13\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-main\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"main-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: ((_sidebarItems$find = sidebarItems.find(item => item.id === activeSection)) === null || _sidebarItems$find === void 0 ? void 0 : _sidebarItems$find.label) || 'Dashboard'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"date-info\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"current-date\",\n            children: new Date().toLocaleDateString('en-US', {\n              weekday: 'long',\n              year: 'numeric',\n              month: 'long',\n              day: 'numeric'\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"main-content\",\n        children: [activeSection === 'overview' && /*#__PURE__*/_jsxDEV(OverviewSection, {\n          stats: stats\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 44\n        }, this), activeSection === 'notes' && /*#__PURE__*/_jsxDEV(NotesSection, {\n          notes: data.notes,\n          onRefresh: fetchAllData\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 41\n        }, this), activeSection === 'habits' && /*#__PURE__*/_jsxDEV(HabitsSection, {\n          habits: data.habits,\n          onRefresh: fetchAllData\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 42\n        }, this), activeSection === 'schedule' && /*#__PURE__*/_jsxDEV(ScheduleSection, {\n          schedule: data.schedule,\n          onRefresh: fetchAllData\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 44\n        }, this), activeSection === 'sections' && /*#__PURE__*/_jsxDEV(SectionsSection, {\n          sections: data.sections,\n          onRefresh: fetchAllData\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 44\n        }, this), activeSection === 'reviews' && /*#__PURE__*/_jsxDEV(ReviewsSection, {\n          reviews: data.reviews,\n          onRefresh: fetchAllData\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 43\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 7\n    }, this), showModal && /*#__PURE__*/_jsxDEV(Modal, {\n      type: modalType,\n      onClose: () => setShowModal(false),\n      onSuccess: () => {\n        setShowModal(false);\n        fetchAllData();\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 154,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 90,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"R+Fm9qONJmqKHmhN6adhds3Nf8E=\", false, function () {\n  return [useAuth];\n});\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useAuth", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "_sidebarItems$find", "user", "activeSection", "setActiveSection", "showModal", "setShowModal", "modalType", "setModalType", "stats", "setStats", "notesCount", "habitsCount", "scheduleCount", "sectionsCount", "reviewsCount", "loading", "setLoading", "data", "setData", "notes", "habits", "schedule", "sections", "reviews", "fetchAllData", "token", "localStorage", "getItem", "notesResponse", "fetch", "headers", "ok", "json", "prev", "length", "error", "console", "sidebarItems", "id", "label", "icon", "quickActions", "handleQuickAction", "actionType", "handleSidebarClick", "sectionId", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "map", "item", "onClick", "action", "find", "Date", "toLocaleDateString", "weekday", "year", "month", "day", "OverviewSection", "NotesSection", "onRefresh", "HabitsSection", "ScheduleSection", "SectionsSection", "ReviewsSection", "Modal", "type", "onClose", "onSuccess", "_c", "$RefreshReg$"], "sources": ["D:/Projects/Notelizer/frontend/src/pages/Dashboard.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useAuth } from '../context/AuthContext';\nimport './Dashboard.css';\n\nconst Dashboard = () => {\n  const { user } = useAuth();\n  const [activeSection, setActiveSection] = useState('overview');\n  const [showModal, setShowModal] = useState(false);\n  const [modalType, setModalType] = useState('');\n  const [stats, setStats] = useState({\n    notesCount: 0,\n    habitsCount: 0,\n    scheduleCount: 0,\n    sectionsCount: 0,\n    reviewsCount: 0\n  });\n  const [loading, setLoading] = useState(true);\n  const [data, setData] = useState({\n    notes: [],\n    habits: [],\n    schedule: [],\n    sections: [],\n    reviews: []\n  });\n\n  useEffect(() => {\n    fetchAllData();\n  }, []);\n\n  const fetchAllData = async () => {\n    try {\n      const token = localStorage.getItem('token');\n\n      // Fetch notes\n      const notesResponse = await fetch('http://localhost:5000/notes', {\n        headers: { 'Authorization': `Bearer ${token}` }\n      });\n\n      if (notesResponse.ok) {\n        const notes = await notesResponse.json();\n        setData(prev => ({ ...prev, notes }));\n        setStats(prev => ({ ...prev, notesCount: notes.length }));\n      }\n\n      // TODO: Add other API calls when backend routes are ready\n\n    } catch (error) {\n      console.error('Error fetching data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const sidebarItems = [\n    { id: 'overview', label: 'Overview', icon: '📊' },\n    { id: 'notes', label: 'My Notes', icon: '📝' },\n    { id: 'habits', label: 'My Habits', icon: '✅' },\n    { id: 'schedule', label: 'My Schedule', icon: '📅' },\n    { id: 'sections', label: 'My Sections', icon: '📚' },\n    { id: 'reviews', label: 'Daily Reviews', icon: '📖' }\n  ];\n\n  const quickActions = [\n    { id: 'note', label: 'Create Note', icon: '📝' },\n    { id: 'habit', label: 'Add Habit', icon: '✅' },\n    { id: 'task', label: 'Schedule Task', icon: '📅' },\n    { id: 'section', label: 'Add Section', icon: '📚' },\n    { id: 'review', label: 'Daily Review', icon: '📖' }\n  ];\n\n  const handleQuickAction = (actionType) => {\n    setModalType(actionType);\n    setShowModal(true);\n  };\n\n  const handleSidebarClick = (sectionId) => {\n    setActiveSection(sectionId);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"dashboard-loading\">\n        <div className=\"loading-spinner\"></div>\n        <p>Loading your dashboard...</p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"dashboard-layout\">\n      {/* Sidebar */}\n      <div className=\"dashboard-sidebar\">\n        <div className=\"sidebar-header\">\n          <h2>Notelizer</h2>\n          <p>Welcome, {user?.name || 'User'}!</p>\n        </div>\n\n        <div className=\"sidebar-nav\">\n          {sidebarItems.map((item) => (\n            <button\n              key={item.id}\n              className={`sidebar-item ${activeSection === item.id ? 'active' : ''}`}\n              onClick={() => handleSidebarClick(item.id)}\n            >\n              <span className=\"sidebar-icon\">{item.icon}</span>\n              <span className=\"sidebar-label\">{item.label}</span>\n            </button>\n          ))}\n        </div>\n\n        <div className=\"sidebar-quick-actions\">\n          <h4>Quick Actions</h4>\n          {quickActions.map((action) => (\n            <button\n              key={action.id}\n              className=\"quick-action-item\"\n              onClick={() => handleQuickAction(action.id)}\n            >\n              <span className=\"action-icon\">{action.icon}</span>\n              <span className=\"action-label\">{action.label}</span>\n            </button>\n          ))}\n        </div>\n      </div>\n\n      {/* Main Content */}\n      <div className=\"dashboard-main\">\n        <div className=\"main-header\">\n          <h1>{sidebarItems.find(item => item.id === activeSection)?.label || 'Dashboard'}</h1>\n          <div className=\"date-info\">\n            <span className=\"current-date\">\n              {new Date().toLocaleDateString('en-US', {\n                weekday: 'long',\n                year: 'numeric',\n                month: 'long',\n                day: 'numeric'\n              })}\n            </span>\n          </div>\n        </div>\n\n        <div className=\"main-content\">\n          {activeSection === 'overview' && <OverviewSection stats={stats} />}\n          {activeSection === 'notes' && <NotesSection notes={data.notes} onRefresh={fetchAllData} />}\n          {activeSection === 'habits' && <HabitsSection habits={data.habits} onRefresh={fetchAllData} />}\n          {activeSection === 'schedule' && <ScheduleSection schedule={data.schedule} onRefresh={fetchAllData} />}\n          {activeSection === 'sections' && <SectionsSection sections={data.sections} onRefresh={fetchAllData} />}\n          {activeSection === 'reviews' && <ReviewsSection reviews={data.reviews} onRefresh={fetchAllData} />}\n        </div>\n      </div>\n\n      {/* Modal for Quick Actions */}\n      {showModal && (\n        <Modal\n          type={modalType}\n          onClose={() => setShowModal(false)}\n          onSuccess={() => {\n            setShowModal(false);\n            fetchAllData();\n          }}\n        />\n      )}\n    </div>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAO,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzB,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,kBAAA;EACtB,MAAM;IAAEC;EAAK,CAAC,GAAGN,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACO,aAAa,EAAEC,gBAAgB,CAAC,GAAGV,QAAQ,CAAC,UAAU,CAAC;EAC9D,MAAM,CAACW,SAAS,EAAEC,YAAY,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACa,SAAS,EAAEC,YAAY,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACe,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAC;IACjCiB,UAAU,EAAE,CAAC;IACbC,WAAW,EAAE,CAAC;IACdC,aAAa,EAAE,CAAC;IAChBC,aAAa,EAAE,CAAC;IAChBC,YAAY,EAAE;EAChB,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACwB,IAAI,EAAEC,OAAO,CAAC,GAAGzB,QAAQ,CAAC;IAC/B0B,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,OAAO,EAAE;EACX,CAAC,CAAC;EAEF7B,SAAS,CAAC,MAAM;IACd8B,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;;MAE3C;MACA,MAAMC,aAAa,GAAG,MAAMC,KAAK,CAAC,6BAA6B,EAAE;QAC/DC,OAAO,EAAE;UAAE,eAAe,EAAE,UAAUL,KAAK;QAAG;MAChD,CAAC,CAAC;MAEF,IAAIG,aAAa,CAACG,EAAE,EAAE;QACpB,MAAMZ,KAAK,GAAG,MAAMS,aAAa,CAACI,IAAI,CAAC,CAAC;QACxCd,OAAO,CAACe,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAEd;QAAM,CAAC,CAAC,CAAC;QACrCV,QAAQ,CAACwB,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAEvB,UAAU,EAAES,KAAK,CAACe;QAAO,CAAC,CAAC,CAAC;MAC3D;;MAEA;IAEF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC9C,CAAC,SAAS;MACRnB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMqB,YAAY,GAAG,CACnB;IAAEC,EAAE,EAAE,UAAU;IAAEC,KAAK,EAAE,UAAU;IAAEC,IAAI,EAAE;EAAK,CAAC,EACjD;IAAEF,EAAE,EAAE,OAAO;IAAEC,KAAK,EAAE,UAAU;IAAEC,IAAI,EAAE;EAAK,CAAC,EAC9C;IAAEF,EAAE,EAAE,QAAQ;IAAEC,KAAK,EAAE,WAAW;IAAEC,IAAI,EAAE;EAAI,CAAC,EAC/C;IAAEF,EAAE,EAAE,UAAU;IAAEC,KAAK,EAAE,aAAa;IAAEC,IAAI,EAAE;EAAK,CAAC,EACpD;IAAEF,EAAE,EAAE,UAAU;IAAEC,KAAK,EAAE,aAAa;IAAEC,IAAI,EAAE;EAAK,CAAC,EACpD;IAAEF,EAAE,EAAE,SAAS;IAAEC,KAAK,EAAE,eAAe;IAAEC,IAAI,EAAE;EAAK,CAAC,CACtD;EAED,MAAMC,YAAY,GAAG,CACnB;IAAEH,EAAE,EAAE,MAAM;IAAEC,KAAK,EAAE,aAAa;IAAEC,IAAI,EAAE;EAAK,CAAC,EAChD;IAAEF,EAAE,EAAE,OAAO;IAAEC,KAAK,EAAE,WAAW;IAAEC,IAAI,EAAE;EAAI,CAAC,EAC9C;IAAEF,EAAE,EAAE,MAAM;IAAEC,KAAK,EAAE,eAAe;IAAEC,IAAI,EAAE;EAAK,CAAC,EAClD;IAAEF,EAAE,EAAE,SAAS;IAAEC,KAAK,EAAE,aAAa;IAAEC,IAAI,EAAE;EAAK,CAAC,EACnD;IAAEF,EAAE,EAAE,QAAQ;IAAEC,KAAK,EAAE,cAAc;IAAEC,IAAI,EAAE;EAAK,CAAC,CACpD;EAED,MAAME,iBAAiB,GAAIC,UAAU,IAAK;IACxCpC,YAAY,CAACoC,UAAU,CAAC;IACxBtC,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMuC,kBAAkB,GAAIC,SAAS,IAAK;IACxC1C,gBAAgB,CAAC0C,SAAS,CAAC;EAC7B,CAAC;EAED,IAAI9B,OAAO,EAAE;IACX,oBACElB,OAAA;MAAKiD,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChClD,OAAA;QAAKiD,SAAS,EAAC;MAAiB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACvCtD,OAAA;QAAAkD,QAAA,EAAG;MAAyB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B,CAAC;EAEV;EAEA,oBACEtD,OAAA;IAAKiD,SAAS,EAAC,kBAAkB;IAAAC,QAAA,gBAE/BlD,OAAA;MAAKiD,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChClD,OAAA;QAAKiD,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BlD,OAAA;UAAAkD,QAAA,EAAI;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClBtD,OAAA;UAAAkD,QAAA,GAAG,WAAS,EAAC,CAAA9C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmD,IAAI,KAAI,MAAM,EAAC,GAAC;QAAA;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC,eAENtD,OAAA;QAAKiD,SAAS,EAAC,aAAa;QAAAC,QAAA,EACzBV,YAAY,CAACgB,GAAG,CAAEC,IAAI,iBACrBzD,OAAA;UAEEiD,SAAS,EAAE,gBAAgB5C,aAAa,KAAKoD,IAAI,CAAChB,EAAE,GAAG,QAAQ,GAAG,EAAE,EAAG;UACvEiB,OAAO,EAAEA,CAAA,KAAMX,kBAAkB,CAACU,IAAI,CAAChB,EAAE,CAAE;UAAAS,QAAA,gBAE3ClD,OAAA;YAAMiD,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAEO,IAAI,CAACd;UAAI;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACjDtD,OAAA;YAAMiD,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAEO,IAAI,CAACf;UAAK;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA,GAL9CG,IAAI,CAAChB,EAAE;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAMN,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENtD,OAAA;QAAKiD,SAAS,EAAC,uBAAuB;QAAAC,QAAA,gBACpClD,OAAA;UAAAkD,QAAA,EAAI;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EACrBV,YAAY,CAACY,GAAG,CAAEG,MAAM,iBACvB3D,OAAA;UAEEiD,SAAS,EAAC,mBAAmB;UAC7BS,OAAO,EAAEA,CAAA,KAAMb,iBAAiB,CAACc,MAAM,CAAClB,EAAE,CAAE;UAAAS,QAAA,gBAE5ClD,OAAA;YAAMiD,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAES,MAAM,CAAChB;UAAI;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAClDtD,OAAA;YAAMiD,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAES,MAAM,CAACjB;UAAK;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA,GAL/CK,MAAM,CAAClB,EAAE;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAMR,CACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtD,OAAA;MAAKiD,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BlD,OAAA;QAAKiD,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BlD,OAAA;UAAAkD,QAAA,EAAK,EAAA/C,kBAAA,GAAAqC,YAAY,CAACoB,IAAI,CAACH,IAAI,IAAIA,IAAI,CAAChB,EAAE,KAAKpC,aAAa,CAAC,cAAAF,kBAAA,uBAApDA,kBAAA,CAAsDuC,KAAK,KAAI;QAAW;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACrFtD,OAAA;UAAKiD,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxBlD,OAAA;YAAMiD,SAAS,EAAC,cAAc;YAAAC,QAAA,EAC3B,IAAIW,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,OAAO,EAAE;cACtCC,OAAO,EAAE,MAAM;cACfC,IAAI,EAAE,SAAS;cACfC,KAAK,EAAE,MAAM;cACbC,GAAG,EAAE;YACP,CAAC;UAAC;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENtD,OAAA;QAAKiD,SAAS,EAAC,cAAc;QAAAC,QAAA,GAC1B7C,aAAa,KAAK,UAAU,iBAAIL,OAAA,CAACmE,eAAe;UAACxD,KAAK,EAAEA;QAAM;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACjEjD,aAAa,KAAK,OAAO,iBAAIL,OAAA,CAACoE,YAAY;UAAC9C,KAAK,EAAEF,IAAI,CAACE,KAAM;UAAC+C,SAAS,EAAE1C;QAAa;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACzFjD,aAAa,KAAK,QAAQ,iBAAIL,OAAA,CAACsE,aAAa;UAAC/C,MAAM,EAAEH,IAAI,CAACG,MAAO;UAAC8C,SAAS,EAAE1C;QAAa;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAC7FjD,aAAa,KAAK,UAAU,iBAAIL,OAAA,CAACuE,eAAe;UAAC/C,QAAQ,EAAEJ,IAAI,CAACI,QAAS;UAAC6C,SAAS,EAAE1C;QAAa;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACrGjD,aAAa,KAAK,UAAU,iBAAIL,OAAA,CAACwE,eAAe;UAAC/C,QAAQ,EAAEL,IAAI,CAACK,QAAS;UAAC4C,SAAS,EAAE1C;QAAa;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACrGjD,aAAa,KAAK,SAAS,iBAAIL,OAAA,CAACyE,cAAc;UAAC/C,OAAO,EAAEN,IAAI,CAACM,OAAQ;UAAC2C,SAAS,EAAE1C;QAAa;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/F,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL/C,SAAS,iBACRP,OAAA,CAAC0E,KAAK;MACJC,IAAI,EAAElE,SAAU;MAChBmE,OAAO,EAAEA,CAAA,KAAMpE,YAAY,CAAC,KAAK,CAAE;MACnCqE,SAAS,EAAEA,CAAA,KAAM;QACfrE,YAAY,CAAC,KAAK,CAAC;QACnBmB,YAAY,CAAC,CAAC;MAChB;IAAE;MAAAwB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACpD,EAAA,CAhKID,SAAS;EAAA,QACIH,OAAO;AAAA;AAAAgF,EAAA,GADpB7E,SAAS;AAkKf,eAAeA,SAAS;AAAC,IAAA6E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}