import React, { useState, useEffect } from 'react';
import './Notes.css';

const Notes = () => {
  const [notes, setNotes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [editingNote, setEditingNote] = useState(null);
  const [formData, setFormData] = useState({ title: '', content: '' });
  const [message, setMessage] = useState('');

  useEffect(() => {
    fetchNotes();
  }, []);

  const fetchNotes = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('http://localhost:5001/notes', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setNotes(data);
      } else {
        setMessage('Failed to fetch notes');
      }
    } catch (error) {
      setMessage('Network error');
      console.error('Error fetching notes:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setMessage('');

    if (!formData.title.trim() || !formData.content.trim()) {
      setMessage('Title and content are required');
      return;
    }

    try {
      const token = localStorage.getItem('token');
      const url = editingNote
        ? `http://localhost:5001/notes/${editingNote.id}`
        : 'http://localhost:5001/notes';
      
      const method = editingNote ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(formData)
      });

      const data = await response.json();

      if (response.ok) {
        setMessage(editingNote ? 'Note updated successfully!' : 'Note created successfully!');
        setFormData({ title: '', content: '' });
        setShowForm(false);
        setEditingNote(null);
        fetchNotes(); // Refresh the notes list
      } else {
        setMessage(data.message || 'Operation failed');
      }
    } catch (error) {
      setMessage('Network error');
      console.error('Error saving note:', error);
    }
  };

  const handleEdit = (note) => {
    setEditingNote(note);
    setFormData({ title: note.title, content: note.content });
    setShowForm(true);
  };

  const handleDelete = async (noteId) => {
    if (!window.confirm('Are you sure you want to delete this note?')) {
      return;
    }

    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`http://localhost:5001/notes/${noteId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const data = await response.json();

      if (response.ok) {
        setMessage('Note deleted successfully!');
        fetchNotes(); // Refresh the notes list
      } else {
        setMessage(data.message || 'Delete failed');
      }
    } catch (error) {
      setMessage('Network error');
      console.error('Error deleting note:', error);
    }
  };

  const handleCancel = () => {
    setFormData({ title: '', content: '' });
    setShowForm(false);
    setEditingNote(null);
    setMessage('');
  };

  if (loading) {
    return <div className="loading">Loading notes...</div>;
  }

  return (
    <div className="notes-container">
      <div className="notes-header">
        <h2>My Notes</h2>
        <button 
          className="add-note-btn"
          onClick={() => setShowForm(true)}
        >
          Add New Note
        </button>
      </div>

      {message && (
        <div className={`message ${message.includes('successfully') ? 'success' : 'error'}`}>
          {message}
        </div>
      )}

      {showForm && (
        <div className="note-form-container">
          <form onSubmit={handleSubmit} className="note-form">
            <h3>{editingNote ? 'Edit Note' : 'Create New Note'}</h3>
            <input
              type="text"
              placeholder="Note title"
              value={formData.title}
              onChange={(e) => setFormData({ ...formData, title: e.target.value })}
              required
            />
            <textarea
              placeholder="Note content"
              value={formData.content}
              onChange={(e) => setFormData({ ...formData, content: e.target.value })}
              rows="6"
              required
            />
            <div className="form-buttons">
              <button type="submit" className="save-btn">
                {editingNote ? 'Update Note' : 'Save Note'}
              </button>
              <button type="button" onClick={handleCancel} className="cancel-btn">
                Cancel
              </button>
            </div>
          </form>
        </div>
      )}

      <div className="notes-list">
        {notes.length === 0 ? (
          <div className="no-notes">
            <p>No notes yet. Create your first note!</p>
          </div>
        ) : (
          notes.map((note) => (
            <div key={note.id} className="note-card">
              <div className="note-header">
                <h3>{note.title}</h3>
                <div className="note-actions">
                  <button onClick={() => handleEdit(note)} className="edit-btn">
                    Edit
                  </button>
                  <button onClick={() => handleDelete(note.id)} className="delete-btn">
                    Delete
                  </button>
                </div>
              </div>
              <div className="note-content">
                <p>{note.content}</p>
              </div>
              <div className="note-timestamp">
                {new Date(note.timestamp).toLocaleString()}
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default Notes;
