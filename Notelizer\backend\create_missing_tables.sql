-- Create missing tables for Notelizer
USE Notelizer;

-- Create sections table
CREATE TABLE IF NOT EXISTS sections (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    section_name VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREI<PERSON>N KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_sections (user_id)
);

-- Create subsections table
CREATE TABLE IF NOT EXISTS subsections (
    id INT AUTO_INCREMENT PRIMARY KEY,
    section_id INT NOT NULL,
    subsection_name VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (section_id) REFERENCES sections(id) ON DELETE CASCADE,
    INDEX idx_section_subsections (section_id)
);

-- Create subsection_content table
CREATE TABLE IF NOT EXISTS subsection_content (
    id INT AUTO_INCREMENT PRIMARY KEY,
    subsection_id INT NOT NULL,
    content_type ENUM('note', 'link') NOT NULL,
    content_text TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (subsection_id) REFERENCES subsections(id) ON DELETE CASCADE,
    INDEX idx_subsection_content (subsection_id)
);

-- Create daily_reviews table
CREATE TABLE IF NOT EXISTS daily_reviews (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_reviews (user_id),
    INDEX idx_review_date (created_at)
);

-- Show created tables
SHOW TABLES;

-- Verify table structures
DESCRIBE sections;
DESCRIBE daily_reviews;
