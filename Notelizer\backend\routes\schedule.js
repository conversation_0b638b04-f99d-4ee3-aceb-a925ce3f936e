const express = require('express');
const router = express.Router();
const db = require('../db');
const jwt = require('jsonwebtoken');

// Middleware to verify JWT token
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ message: 'Access token required' });
  }

  jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ message: 'Invalid token' });
    }
    req.user = user;
    next();
  });
};

// Get all scheduled tasks for the authenticated user
router.get('/', authenticateToken, async (req, res) => {
  try {
    const [tasks] = await db.query(
      'SELECT * FROM schedule WHERE user_id = ? ORDER BY scheduled_time ASC',
      [req.user.id]
    );
    res.json(tasks);
  } catch (error) {
    console.error('Error fetching schedule:', error);
    res.status(500).json({ message: 'Database error' });
  }
});

// Create a new scheduled task
router.post('/', authenticateToken, async (req, res) => {
  const { task, scheduled_time } = req.body;

  if (!task || !scheduled_time) {
    return res.status(400).json({ message: 'Task and scheduled time are required' });
  }

  try {
    const [result] = await db.query(
      'INSERT INTO schedule (user_id, task, scheduled_time, is_done, created_at) VALUES (?, ?, ?, FALSE, NOW())',
      [req.user.id, task, scheduled_time]
    );

    res.status(201).json({
      message: 'Task scheduled successfully',
      taskId: result.insertId
    });
  } catch (error) {
    console.error('Error creating scheduled task:', error);
    res.status(500).json({ message: 'Database error' });
  }
});

// Toggle task completion status
router.put('/:id/toggle', authenticateToken, async (req, res) => {
  const taskId = req.params.id;

  try {
    // First get current status
    const [tasks] = await db.query(
      'SELECT is_done FROM schedule WHERE id = ? AND user_id = ?',
      [taskId, req.user.id]
    );

    if (tasks.length === 0) {
      return res.status(404).json({ message: 'Task not found or unauthorized' });
    }

    const newStatus = !tasks[0].is_done;

    const [result] = await db.query(
      'UPDATE schedule SET is_done = ? WHERE id = ? AND user_id = ?',
      [newStatus, taskId, req.user.id]
    );

    res.json({
      message: 'Task status updated successfully',
      is_done: newStatus
    });
  } catch (error) {
    console.error('Error updating task:', error);
    res.status(500).json({ message: 'Database error' });
  }
});

// Update a scheduled task
router.put('/:id', authenticateToken, async (req, res) => {
  const { task, scheduled_time } = req.body;
  const taskId = req.params.id;

  if (!task || !scheduled_time) {
    return res.status(400).json({ message: 'Task and scheduled time are required' });
  }

  try {
    const [result] = await db.query(
      'UPDATE schedule SET task = ?, scheduled_time = ? WHERE id = ? AND user_id = ?',
      [task, scheduled_time, taskId, req.user.id]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({ message: 'Task not found or unauthorized' });
    }

    res.json({ message: 'Task updated successfully' });
  } catch (error) {
    console.error('Error updating task:', error);
    res.status(500).json({ message: 'Database error' });
  }
});

// Delete a scheduled task
router.delete('/:id', authenticateToken, async (req, res) => {
  const taskId = req.params.id;

  try {
    const [result] = await db.query(
      'DELETE FROM schedule WHERE id = ? AND user_id = ?',
      [taskId, req.user.id]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({ message: 'Task not found or unauthorized' });
    }

    res.json({ message: 'Task deleted successfully' });
  } catch (error) {
    console.error('Error deleting task:', error);
    res.status(500).json({ message: 'Database error' });
  }
});

// Get upcoming tasks (next 7 days)
router.get('/upcoming', authenticateToken, async (req, res) => {
  try {
    const [tasks] = await db.query(
      `SELECT * FROM schedule
       WHERE user_id = ?
       AND scheduled_time >= NOW()
       AND scheduled_time <= DATE_ADD(NOW(), INTERVAL 7 DAY)
       AND is_done = FALSE
       ORDER BY scheduled_time ASC`,
      [req.user.id]
    );
    res.json(tasks);
  } catch (error) {
    console.error('Error fetching upcoming tasks:', error);
    res.status(500).json({ message: 'Database error' });
  }
});

module.exports = router;
