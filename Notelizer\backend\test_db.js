// Database Connection Test
const db = require('./db');

async function testDatabase() {
  console.log('🔍 Testing database connection...');
  
  try {
    // Test basic connection
    const [rows] = await db.query('SELECT 1 as test');
    console.log('✅ Database connection successful');
    
    // Check if database exists
    const [databases] = await db.query('SHOW DATABASES LIKE "Notelizer"');
    if (databases.length === 0) {
      console.log('❌ Database "Notelizer" does not exist');
      console.log('Please create it with: CREATE DATABASE Notelizer;');
      return;
    }
    console.log('✅ Database "Notelizer" exists');
    
    // Check if we're using the correct database
    await db.query('USE Notelizer');
    console.log('✅ Using Notelizer database');
    
    // Check if required tables exist
    const requiredTables = ['users', 'notes', 'habits', 'schedule', 'sections', 'daily_reviews'];
    const [tables] = await db.query('SHOW TABLES');
    const existingTables = tables.map(row => Object.values(row)[0]);
    
    console.log('\n📋 Table Status:');
    for (const table of requiredTables) {
      if (existingTables.includes(table)) {
        console.log(`✅ ${table} - exists`);
      } else {
        console.log(`❌ ${table} - missing`);
      }
    }
    
    // Test a simple query on users table
    try {
      const [users] = await db.query('SELECT COUNT(*) as count FROM users');
      console.log(`\n👥 Users in database: ${users[0].count}`);
    } catch (error) {
      console.log('❌ Error querying users table:', error.message);
    }
    
    console.log('\n🎉 Database test completed');
    
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    console.log('\n🔧 Troubleshooting tips:');
    console.log('1. Make sure MySQL is running');
    console.log('2. Check your .env file credentials');
    console.log('3. Ensure the Notelizer database exists');
    console.log('4. Run the database_setup.sql script');
  }
  
  process.exit(0);
}

testDatabase();
