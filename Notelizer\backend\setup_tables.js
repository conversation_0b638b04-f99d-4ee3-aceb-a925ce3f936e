// Setup missing database tables
const db = require('./db');
const fs = require('fs');

async function setupTables() {
  console.log('🔧 Setting up missing database tables...');
  
  try {
    // Read and execute the SQL file
    const sqlContent = fs.readFileSync('./create_missing_tables.sql', 'utf8');
    
    // Split by semicolon and execute each statement
    const statements = sqlContent.split(';').filter(stmt => stmt.trim().length > 0);
    
    for (const statement of statements) {
      const trimmedStatement = statement.trim();
      if (trimmedStatement) {
        try {
          await db.query(trimmedStatement);
          console.log('✅ Executed:', trimmedStatement.split('\n')[0].substring(0, 50) + '...');
        } catch (error) {
          if (!error.message.includes('already exists')) {
            console.log('⚠️  Warning:', error.message);
          }
        }
      }
    }
    
    // Verify tables were created
    console.log('\n🔍 Verifying tables...');
    const [tables] = await db.query('SHOW TABLES');
    const tableNames = tables.map(row => Object.values(row)[0]);
    
    const requiredTables = ['users', 'notes', 'habits', 'schedule', 'sections', 'subsections', 'subsection_content', 'daily_reviews'];
    
    for (const table of requiredTables) {
      if (tableNames.includes(table)) {
        console.log(`✅ ${table}`);
      } else {
        console.log(`❌ ${table} - still missing`);
      }
    }
    
    console.log('\n🎉 Database setup completed successfully!');
    
  } catch (error) {
    console.error('❌ Error setting up tables:', error.message);
  }
  
  process.exit(0);
}

setupTables();
