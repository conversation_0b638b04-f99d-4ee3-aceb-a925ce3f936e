{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\Notelizer\\\\frontend\\\\src\\\\pages\\\\UserLogin.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UserLogin = () => {\n  _s();\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [message, setMessage] = useState('');\n  const [loading, setLoading] = useState(false);\n  const navigate = useNavigate();\n  const {\n    login\n  } = useAuth();\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setMessage('');\n    setLoading(true);\n    try {\n      const res = await fetch('http://localhost:5001/auth/login', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          email,\n          password\n        })\n      });\n      const data = await res.json();\n      if (res.ok) {\n        // Use user info from backend response\n        login(data.token, data.user);\n        setMessage('Login successful! Redirecting...');\n\n        // Redirect to dashboard after a short delay\n        setTimeout(() => {\n          navigate('/dashboard');\n        }, 1000);\n      } else {\n        setMessage(data.message || 'Login failed');\n      }\n    } catch (error) {\n      setMessage('Network error. Please try again.');\n      console.error('Login error:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"form-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"User Login\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"email\",\n        placeholder: \"Email\",\n        value: email,\n        onChange: e => setEmail(e.target.value),\n        required: true,\n        disabled: loading\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"password\",\n        placeholder: \"Password\",\n        value: password,\n        onChange: e => setPassword(e.target.value),\n        required: true,\n        disabled: loading\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        disabled: loading,\n        children: loading ? 'Logging in...' : 'Login'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this), message && /*#__PURE__*/_jsxDEV(\"p\", {\n      className: message.includes('successful') ? 'success-message' : 'error-message',\n      children: message\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 19\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 48,\n    columnNumber: 5\n  }, this);\n};\n_s(UserLogin, \"jpNczAvuu9g3ZgtWmUTmpy+mk58=\", false, function () {\n  return [useNavigate, useAuth];\n});\n_c = UserLogin;\nexport default UserLogin;\nvar _c;\n$RefreshReg$(_c, \"UserLogin\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "useAuth", "jsxDEV", "_jsxDEV", "UserLogin", "_s", "email", "setEmail", "password", "setPassword", "message", "setMessage", "loading", "setLoading", "navigate", "login", "handleSubmit", "e", "preventDefault", "res", "fetch", "method", "headers", "body", "JSON", "stringify", "data", "json", "ok", "token", "user", "setTimeout", "error", "console", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "type", "placeholder", "value", "onChange", "target", "required", "disabled", "includes", "_c", "$RefreshReg$"], "sources": ["D:/Projects/Notelizer/frontend/src/pages/UserLogin.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\n\nconst UserLogin = () => {\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [message, setMessage] = useState('');\n  const [loading, setLoading] = useState(false);\n  const navigate = useNavigate();\n  const { login } = useAuth();\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setMessage('');\n    setLoading(true);\n\n    try {\n      const res = await fetch('http://localhost:5001/auth/login', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ email, password })\n      });\n\n      const data = await res.json();\n\n      if (res.ok) {\n        // Use user info from backend response\n        login(data.token, data.user);\n        setMessage('Login successful! Redirecting...');\n\n        // Redirect to dashboard after a short delay\n        setTimeout(() => {\n          navigate('/dashboard');\n        }, 1000);\n      } else {\n        setMessage(data.message || 'Login failed');\n      }\n    } catch (error) {\n      setMessage('Network error. Please try again.');\n      console.error('Login error:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"form-container\">\n      <h2>User Login</h2>\n      <form onSubmit={handleSubmit}>\n        <input\n          type=\"email\"\n          placeholder=\"Email\"\n          value={email}\n          onChange={e => setEmail(e.target.value)}\n          required\n          disabled={loading}\n        />\n        <input\n          type=\"password\"\n          placeholder=\"Password\"\n          value={password}\n          onChange={e => setPassword(e.target.value)}\n          required\n          disabled={loading}\n        />\n        <button type=\"submit\" disabled={loading}>\n          {loading ? 'Logging in...' : 'Login'}\n        </button>\n      </form>\n      {message && <p className={message.includes('successful') ? 'success-message' : 'error-message'}>{message}</p>}\n    </div>\n  );\n};\n\nexport default UserLogin;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACS,QAAQ,EAAEC,WAAW,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACa,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAMe,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEe;EAAM,CAAC,GAAGd,OAAO,CAAC,CAAC;EAE3B,MAAMe,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBP,UAAU,CAAC,EAAE,CAAC;IACdE,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF,MAAMM,GAAG,GAAG,MAAMC,KAAK,CAAC,kCAAkC,EAAE;QAC1DC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UAAE,cAAc,EAAE;QAAmB,CAAC;QAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UAAEnB,KAAK;UAAEE;QAAS,CAAC;MAC1C,CAAC,CAAC;MAEF,MAAMkB,IAAI,GAAG,MAAMP,GAAG,CAACQ,IAAI,CAAC,CAAC;MAE7B,IAAIR,GAAG,CAACS,EAAE,EAAE;QACV;QACAb,KAAK,CAACW,IAAI,CAACG,KAAK,EAAEH,IAAI,CAACI,IAAI,CAAC;QAC5BnB,UAAU,CAAC,kCAAkC,CAAC;;QAE9C;QACAoB,UAAU,CAAC,MAAM;UACfjB,QAAQ,CAAC,YAAY,CAAC;QACxB,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACLH,UAAU,CAACe,IAAI,CAAChB,OAAO,IAAI,cAAc,CAAC;MAC5C;IACF,CAAC,CAAC,OAAOsB,KAAK,EAAE;MACdrB,UAAU,CAAC,kCAAkC,CAAC;MAC9CsB,OAAO,CAACD,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;IACtC,CAAC,SAAS;MACRnB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACEV,OAAA;IAAK+B,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC7BhC,OAAA;MAAAgC,QAAA,EAAI;IAAU;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACnBpC,OAAA;MAAMqC,QAAQ,EAAExB,YAAa;MAAAmB,QAAA,gBAC3BhC,OAAA;QACEsC,IAAI,EAAC,OAAO;QACZC,WAAW,EAAC,OAAO;QACnBC,KAAK,EAAErC,KAAM;QACbsC,QAAQ,EAAE3B,CAAC,IAAIV,QAAQ,CAACU,CAAC,CAAC4B,MAAM,CAACF,KAAK,CAAE;QACxCG,QAAQ;QACRC,QAAQ,EAAEnC;MAAQ;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC,eACFpC,OAAA;QACEsC,IAAI,EAAC,UAAU;QACfC,WAAW,EAAC,UAAU;QACtBC,KAAK,EAAEnC,QAAS;QAChBoC,QAAQ,EAAE3B,CAAC,IAAIR,WAAW,CAACQ,CAAC,CAAC4B,MAAM,CAACF,KAAK,CAAE;QAC3CG,QAAQ;QACRC,QAAQ,EAAEnC;MAAQ;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC,eACFpC,OAAA;QAAQsC,IAAI,EAAC,QAAQ;QAACM,QAAQ,EAAEnC,OAAQ;QAAAuB,QAAA,EACrCvB,OAAO,GAAG,eAAe,GAAG;MAAO;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EACN7B,OAAO,iBAAIP,OAAA;MAAG+B,SAAS,EAAExB,OAAO,CAACsC,QAAQ,CAAC,YAAY,CAAC,GAAG,iBAAiB,GAAG,eAAgB;MAAAb,QAAA,EAAEzB;IAAO;MAAA0B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC1G,CAAC;AAEV,CAAC;AAAClC,EAAA,CArEID,SAAS;EAAA,QAKIJ,WAAW,EACVC,OAAO;AAAA;AAAAgD,EAAA,GANrB7C,SAAS;AAuEf,eAAeA,SAAS;AAAC,IAAA6C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}