// Minimal test server
const express = require('express');
const app = express();

app.use(express.json());

app.get('/health', (req, res) => {
  console.log('Health endpoint hit');
  res.json({ status: 'OK', message: 'Test server working' });
});

app.get('/test', (req, res) => {
  res.json({ message: 'Test endpoint working' });
});

const PORT = 5001;
app.listen(PORT, () => {
  console.log(`Test server running on port ${PORT}`);
});

// Test the server after 1 second
setTimeout(async () => {
  const http = require('http');
  
  const options = {
    hostname: 'localhost',
    port: 5001,
    path: '/health',
    method: 'GET'
  };

  const req = http.request(options, (res) => {
    let body = '';
    res.on('data', (chunk) => {
      body += chunk;
    });
    res.on('end', () => {
      console.log('Test response:', body);
      process.exit(0);
    });
  });

  req.on('error', (error) => {
    console.error('Test failed:', error);
    process.exit(1);
  });

  req.end();
}, 1000);
