const express = require('express');
const router = express.Router();
const db = require('../db');
const jwt = require('jsonwebtoken');

// Middleware to verify JWT token
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ message: 'Access token required' });
  }

  jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ message: 'Invalid token' });
    }
    req.user = user;
    next();
  });
};

// Get all daily reviews for the authenticated user
router.get('/', authenticateToken, async (req, res) => {
  try {
    const [reviews] = await db.query(
      'SELECT * FROM daily_reviews WHERE user_id = ? ORDER BY created_at DESC',
      [req.user.id]
    );
    res.json(reviews);
  } catch (error) {
    console.error('Error fetching reviews:', error);
    res.status(500).json({ message: 'Database error' });
  }
});

// Create a new daily review
router.post('/', authenticateToken, async (req, res) => {
  const { title, content } = req.body;
  
  if (!title || !content) {
    return res.status(400).json({ message: 'Title and content are required' });
  }

  try {
    const [result] = await db.query(
      'INSERT INTO daily_reviews (user_id, title, content, created_at) VALUES (?, ?, ?, NOW())',
      [req.user.id, title, content]
    );
    
    res.status(201).json({ 
      message: 'Daily review created successfully',
      reviewId: result.insertId 
    });
  } catch (error) {
    console.error('Error creating review:', error);
    res.status(500).json({ message: 'Database error' });
  }
});

// Update a daily review
router.put('/:id', authenticateToken, async (req, res) => {
  const { title, content } = req.body;
  const reviewId = req.params.id;

  if (!title || !content) {
    return res.status(400).json({ message: 'Title and content are required' });
  }

  try {
    const [result] = await db.query(
      'UPDATE daily_reviews SET title = ?, content = ? WHERE id = ? AND user_id = ?',
      [title, content, reviewId, req.user.id]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({ message: 'Review not found or unauthorized' });
    }

    res.json({ message: 'Review updated successfully' });
  } catch (error) {
    console.error('Error updating review:', error);
    res.status(500).json({ message: 'Database error' });
  }
});

// Delete a daily review
router.delete('/:id', authenticateToken, async (req, res) => {
  const reviewId = req.params.id;

  try {
    const [result] = await db.query(
      'DELETE FROM daily_reviews WHERE id = ? AND user_id = ?',
      [reviewId, req.user.id]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({ message: 'Review not found or unauthorized' });
    }

    res.json({ message: 'Review deleted successfully' });
  } catch (error) {
    console.error('Error deleting review:', error);
    res.status(500).json({ message: 'Database error' });
  }
});

module.exports = router;
