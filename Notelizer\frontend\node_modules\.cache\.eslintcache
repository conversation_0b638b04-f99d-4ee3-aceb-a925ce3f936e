[{"D:\\Projects\\Notelizer\\frontend\\src\\index.js": "1", "D:\\Projects\\Notelizer\\frontend\\src\\App.js": "2", "D:\\Projects\\Notelizer\\frontend\\src\\reportWebVitals.js": "3", "D:\\Projects\\Notelizer\\frontend\\src\\pages\\UserLogin.jsx": "4", "D:\\Projects\\Notelizer\\frontend\\src\\pages\\AdminSignup.jsx": "5", "D:\\Projects\\Notelizer\\frontend\\src\\components\\Navbar.jsx": "6", "D:\\Projects\\Notelizer\\frontend\\src\\pages\\AdminDashboard.jsx": "7", "D:\\Projects\\Notelizer\\frontend\\src\\pages\\UserSignup.jsx": "8", "D:\\Projects\\Notelizer\\frontend\\src\\pages\\AdminLogin.jsx": "9", "D:\\Projects\\Notelizer\\frontend\\src\\pages\\Landing.jsx": "10", "D:\\Projects\\Notelizer\\frontend\\src\\pages\\Dashboard.jsx": "11", "D:\\Projects\\Notelizer\\frontend\\src\\context\\AuthContext.js": "12", "D:\\Projects\\Notelizer\\frontend\\src\\components\\ProtectedRoute.jsx": "13", "D:\\Projects\\Notelizer\\frontend\\src\\pages\\Notes.jsx": "14"}, {"size": 535, "mtime": 1750754269266, "results": "15", "hashOfConfig": "16"}, {"size": 1579, "mtime": 1750947755633, "results": "17", "hashOfConfig": "16"}, {"size": 362, "mtime": 1750754269379, "results": "18", "hashOfConfig": "16"}, {"size": 2142, "mtime": 1751090774555, "results": "19", "hashOfConfig": "16"}, {"size": 1311, "mtime": 1750863499234, "results": "20", "hashOfConfig": "16"}, {"size": 886, "mtime": 1750947595063, "results": "21", "hashOfConfig": "16"}, {"size": 214, "mtime": 1750863505613, "results": "22", "hashOfConfig": "16"}, {"size": 2262, "mtime": 1751090789388, "results": "23", "hashOfConfig": "16"}, {"size": 1172, "mtime": 1750863494727, "results": "24", "hashOfConfig": "16"}, {"size": 2477, "mtime": 1750953194296, "results": "25", "hashOfConfig": "16"}, {"size": 17729, "mtime": 1751090761006, "results": "26", "hashOfConfig": "16"}, {"size": 1445, "mtime": 1750947363639, "results": "27", "hashOfConfig": "16"}, {"size": 815, "mtime": 1750947545004, "results": "28", "hashOfConfig": "16"}, {"size": 5940, "mtime": 1751090828035, "results": "29", "hashOfConfig": "16"}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1lmf<PERSON>y", {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Projects\\Notelizer\\frontend\\src\\index.js", [], [], "D:\\Projects\\Notelizer\\frontend\\src\\App.js", [], [], "D:\\Projects\\Notelizer\\frontend\\src\\reportWebVitals.js", [], [], "D:\\Projects\\Notelizer\\frontend\\src\\pages\\UserLogin.jsx", [], [], "D:\\Projects\\Notelizer\\frontend\\src\\pages\\AdminSignup.jsx", [], [], "D:\\Projects\\Notelizer\\frontend\\src\\components\\Navbar.jsx", [], [], "D:\\Projects\\Notelizer\\frontend\\src\\pages\\AdminDashboard.jsx", [], [], "D:\\Projects\\Notelizer\\frontend\\src\\pages\\UserSignup.jsx", [], [], "D:\\Projects\\Notelizer\\frontend\\src\\pages\\AdminLogin.jsx", [], [], "D:\\Projects\\Notelizer\\frontend\\src\\pages\\Landing.jsx", [], [], "D:\\Projects\\Notelizer\\frontend\\src\\pages\\Dashboard.jsx", [], [], "D:\\Projects\\Notelizer\\frontend\\src\\context\\AuthContext.js", [], [], "D:\\Projects\\Notelizer\\frontend\\src\\components\\ProtectedRoute.jsx", [], [], "D:\\Projects\\Notelizer\\frontend\\src\\pages\\Notes.jsx", [], []]