const express = require('express');
const cors = require('cors');
require('dotenv').config();

// Test route imports one by one
console.log('Loading routes...');
const authRoutes = require('./routes/auth');
console.log('✅ Auth routes loaded');
const notesRoutes = require('./routes/notes');
console.log('✅ Notes routes loaded');

// Comment out potentially problematic routes for testing
try {
  const habitsRoutes = require('./routes/habits');
  console.log('✅ Habits routes loaded');
} catch (error) {
  console.error('❌ Error loading habits routes:', error.message);
}

try {
  const scheduleRoutes = require('./routes/schedule');
  console.log('✅ Schedule routes loaded');
} catch (error) {
  console.error('❌ Error loading schedule routes:', error.message);
}

try {
  const sectionsRoutes = require('./routes/sections');
  console.log('✅ Sections routes loaded');
} catch (error) {
  console.error('❌ Error loading sections routes:', error.message);
}

try {
  const reviewsRoutes = require('./routes/reviews');
  console.log('✅ Reviews routes loaded');
} catch (error) {
  console.error('❌ Error loading reviews routes:', error.message);
}

const app = express();

// Enable CORS for all routes
app.use(cors({
  origin: 'http://localhost:3000',
  credentials: true
}));

// Parse JSON bodies
app.use(express.json());

// Add request logging middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'Notelizer API is running',
    timestamp: new Date().toISOString()
  });
});

// API routes
console.log('Setting up routes...');
app.use('/auth', authRoutes);
app.use('/notes', notesRoutes);

// Only add routes that loaded successfully
try {
  const habitsRoutes = require('./routes/habits');
  app.use('/habits', habitsRoutes);
  console.log('✅ Habits routes registered');
} catch (error) {
  console.log('⚠️  Skipping habits routes');
}

try {
  const scheduleRoutes = require('./routes/schedule');
  app.use('/schedule', scheduleRoutes);
  console.log('✅ Schedule routes registered');
} catch (error) {
  console.log('⚠️  Skipping schedule routes');
}

try {
  const sectionsRoutes = require('./routes/sections');
  app.use('/sections', sectionsRoutes);
  console.log('✅ Sections routes registered');
} catch (error) {
  console.log('⚠️  Skipping sections routes');
}

try {
  const reviewsRoutes = require('./routes/reviews');
  app.use('/reviews', reviewsRoutes);
  console.log('✅ Reviews routes registered');
} catch (error) {
  console.log('⚠️  Skipping reviews routes');
}

// Add error handling middleware (must be last)
app.use((err, req, res, next) => {
  console.error('Error:', err);
  res.status(500).json({
    message: 'Internal server error',
    error: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
  });
});

const PORT = process.env.PORT || 5001;
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log(`Health check: http://localhost:${PORT}/health`);
});
