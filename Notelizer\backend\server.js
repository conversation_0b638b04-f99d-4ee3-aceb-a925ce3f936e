const express = require('express');
const cors = require('cors');
require('dotenv').config();
const authRoutes = require('./routes/auth');
const notesRoutes = require('./routes/notes');
const habitsRoutes = require('./routes/habits');
const scheduleRoutes = require('./routes/schedule');
const sectionsRoutes = require('./routes/sections');
const reviewsRoutes = require('./routes/reviews');

const app = express();
app.use(cors());
app.use(express.json());

app.use('/auth', authRoutes);
app.use('/notes', notesRoutes);
app.use('/habits', habitsRoutes);
app.use('/schedule', scheduleRoutes);
app.use('/sections', sectionsRoutes);
app.use('/reviews', reviewsRoutes);

const PORT = 5000;
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});
