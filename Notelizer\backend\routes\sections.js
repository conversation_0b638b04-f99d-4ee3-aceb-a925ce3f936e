const express = require('express');
const router = express.Router();
const db = require('../db');
const jwt = require('jsonwebtoken');

// Middleware to verify JWT token
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ message: 'Access token required' });
  }

  jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ message: 'Invalid token' });
    }
    req.user = user;
    next();
  });
};

// Get all sections for the authenticated user
router.get('/', authenticateToken, async (req, res) => {
  try {
    const [sections] = await db.query(
      'SELECT * FROM sections WHERE user_id = ? ORDER BY created_at DESC',
      [req.user.id]
    );
    res.json(sections);
  } catch (error) {
    console.error('Error fetching sections:', error);
    res.status(500).json({ message: 'Database error' });
  }
});

// Create a new section
router.post('/', authenticateToken, async (req, res) => {
  const { section_name } = req.body;
  
  if (!section_name) {
    return res.status(400).json({ message: 'Section name is required' });
  }

  try {
    const [result] = await db.query(
      'INSERT INTO sections (user_id, section_name, created_at) VALUES (?, ?, NOW())',
      [req.user.id, section_name]
    );
    
    res.status(201).json({ 
      message: 'Section created successfully',
      sectionId: result.insertId 
    });
  } catch (error) {
    console.error('Error creating section:', error);
    res.status(500).json({ message: 'Database error' });
  }
});

// Get subsections for a specific section
router.get('/:sectionId/subsections', authenticateToken, async (req, res) => {
  const { sectionId } = req.params;

  try {
    // Verify section belongs to user
    const [sections] = await db.query(
      'SELECT * FROM sections WHERE id = ? AND user_id = ?',
      [sectionId, req.user.id]
    );

    if (sections.length === 0) {
      return res.status(404).json({ message: 'Section not found' });
    }

    const [subsections] = await db.query(
      'SELECT * FROM subsections WHERE section_id = ? ORDER BY created_at DESC',
      [sectionId]
    );
    
    res.json(subsections);
  } catch (error) {
    console.error('Error fetching subsections:', error);
    res.status(500).json({ message: 'Database error' });
  }
});

// Create a new subsection
router.post('/:sectionId/subsections', authenticateToken, async (req, res) => {
  const { sectionId } = req.params;
  const { subsection_name } = req.body;
  
  if (!subsection_name) {
    return res.status(400).json({ message: 'Subsection name is required' });
  }

  try {
    // Verify section belongs to user
    const [sections] = await db.query(
      'SELECT * FROM sections WHERE id = ? AND user_id = ?',
      [sectionId, req.user.id]
    );

    if (sections.length === 0) {
      return res.status(404).json({ message: 'Section not found' });
    }

    const [result] = await db.query(
      'INSERT INTO subsections (section_id, subsection_name, created_at) VALUES (?, ?, NOW())',
      [sectionId, subsection_name]
    );
    
    res.status(201).json({ 
      message: 'Subsection created successfully',
      subsectionId: result.insertId 
    });
  } catch (error) {
    console.error('Error creating subsection:', error);
    res.status(500).json({ message: 'Database error' });
  }
});

// Delete a section
router.delete('/:id', authenticateToken, async (req, res) => {
  const sectionId = req.params.id;

  try {
    const [result] = await db.query(
      'DELETE FROM sections WHERE id = ? AND user_id = ?',
      [sectionId, req.user.id]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({ message: 'Section not found or unauthorized' });
    }

    res.json({ message: 'Section deleted successfully' });
  } catch (error) {
    console.error('Error deleting section:', error);
    res.status(500).json({ message: 'Database error' });
  }
});

module.exports = router;
