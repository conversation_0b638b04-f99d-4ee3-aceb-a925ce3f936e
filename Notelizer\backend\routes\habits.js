const express = require('express');
const router = express.Router();
const db = require('../db');
const jwt = require('jsonwebtoken');

// Middleware to verify JWT token
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ message: 'Access token required' });
  }

  jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ message: 'Invalid token' });
    }
    req.user = user;
    next();
  });
};

// Get all habits for the authenticated user
router.get('/', authenticateToken, async (req, res) => {
  try {
    const [habits] = await db.query(
      'SELECT * FROM habits WHERE user_id = ? ORDER BY created_at DESC',
      [req.user.id]
    );
    res.json(habits);
  } catch (error) {
    console.error('Error fetching habits:', error);
    res.status(500).json({ message: 'Database error' });
  }
});

// Create a new habit
router.post('/', authenticateToken, async (req, res) => {
  const { habit_name } = req.body;

  if (!habit_name) {
    return res.status(400).json({ message: 'Habit name is required' });
  }

  try {
    const [result] = await db.query(
      'INSERT INTO habits (user_id, habit_name, is_completed, created_at) VALUES (?, ?, FALSE, NOW())',
      [req.user.id, habit_name]
    );

    res.status(201).json({
      message: 'Habit created successfully',
      habitId: result.insertId
    });
  } catch (error) {
    console.error('Error creating habit:', error);
    res.status(500).json({ message: 'Database error' });
  }
});

// Toggle habit completion status
router.put('/:id/toggle', authenticateToken, async (req, res) => {
  const habitId = req.params.id;

  try {
    // First get current status
    const [habits] = await db.query(
      'SELECT is_completed FROM habits WHERE id = ? AND user_id = ?',
      [habitId, req.user.id]
    );

    if (habits.length === 0) {
      return res.status(404).json({ message: 'Habit not found or unauthorized' });
    }

    const newStatus = !habits[0].is_completed;

    const [result] = await db.query(
      'UPDATE habits SET is_completed = ? WHERE id = ? AND user_id = ?',
      [newStatus, habitId, req.user.id]
    );

    res.json({
      message: 'Habit status updated successfully',
      is_completed: newStatus
    });
  } catch (error) {
    console.error('Error updating habit:', error);
    res.status(500).json({ message: 'Database error' });
  }
});

// Update habit name
router.put('/:id', authenticateToken, async (req, res) => {
  const { habit_name } = req.body;
  const habitId = req.params.id;

  if (!habit_name) {
    return res.status(400).json({ message: 'Habit name is required' });
  }

  try {
    const [result] = await db.query(
      'UPDATE habits SET habit_name = ? WHERE id = ? AND user_id = ?',
      [habit_name, habitId, req.user.id]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({ message: 'Habit not found or unauthorized' });
    }

    res.json({ message: 'Habit updated successfully' });
  } catch (error) {
    console.error('Error updating habit:', error);
    res.status(500).json({ message: 'Database error' });
  }
});

// Delete a habit
router.delete('/:id', authenticateToken, async (req, res) => {
  const habitId = req.params.id;

  try {
    const [result] = await db.query(
      'DELETE FROM habits WHERE id = ? AND user_id = ?',
      [habitId, req.user.id]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({ message: 'Habit not found or unauthorized' });
    }

    res.json({ message: 'Habit deleted successfully' });
  } catch (error) {
    console.error('Error deleting habit:', error);
    res.status(500).json({ message: 'Database error' });
  }
});

module.exports = router;
