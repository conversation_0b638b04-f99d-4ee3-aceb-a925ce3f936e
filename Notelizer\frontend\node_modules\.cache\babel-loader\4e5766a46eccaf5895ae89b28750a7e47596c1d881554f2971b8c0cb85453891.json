{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\Notelizer\\\\frontend\\\\src\\\\pages\\\\Notes.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport './Notes.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Notes = () => {\n  _s();\n  const [notes, setNotes] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [showForm, setShowForm] = useState(false);\n  const [editingNote, setEditingNote] = useState(null);\n  const [formData, setFormData] = useState({\n    title: '',\n    content: ''\n  });\n  const [message, setMessage] = useState('');\n  useEffect(() => {\n    fetchNotes();\n  }, []);\n  const fetchNotes = async () => {\n    try {\n      const token = localStorage.getItem('token');\n      const response = await fetch('http://localhost:5001/notes', {\n        headers: {\n          'Authorization': `Bear<PERSON> ${token}`\n        }\n      });\n      if (response.ok) {\n        const data = await response.json();\n        setNotes(data);\n      } else {\n        setMessage('Failed to fetch notes');\n      }\n    } catch (error) {\n      setMessage('Network error');\n      console.error('Error fetching notes:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setMessage('');\n    if (!formData.title.trim() || !formData.content.trim()) {\n      setMessage('Title and content are required');\n      return;\n    }\n    try {\n      const token = localStorage.getItem('token');\n      const url = editingNote ? `http://localhost:5000/notes/${editingNote.id}` : 'http://localhost:5000/notes';\n      const method = editingNote ? 'PUT' : 'POST';\n      const response = await fetch(url, {\n        method,\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${token}`\n        },\n        body: JSON.stringify(formData)\n      });\n      const data = await response.json();\n      if (response.ok) {\n        setMessage(editingNote ? 'Note updated successfully!' : 'Note created successfully!');\n        setFormData({\n          title: '',\n          content: ''\n        });\n        setShowForm(false);\n        setEditingNote(null);\n        fetchNotes(); // Refresh the notes list\n      } else {\n        setMessage(data.message || 'Operation failed');\n      }\n    } catch (error) {\n      setMessage('Network error');\n      console.error('Error saving note:', error);\n    }\n  };\n  const handleEdit = note => {\n    setEditingNote(note);\n    setFormData({\n      title: note.title,\n      content: note.content\n    });\n    setShowForm(true);\n  };\n  const handleDelete = async noteId => {\n    if (!window.confirm('Are you sure you want to delete this note?')) {\n      return;\n    }\n    try {\n      const token = localStorage.getItem('token');\n      const response = await fetch(`http://localhost:5000/notes/${noteId}`, {\n        method: 'DELETE',\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      const data = await response.json();\n      if (response.ok) {\n        setMessage('Note deleted successfully!');\n        fetchNotes(); // Refresh the notes list\n      } else {\n        setMessage(data.message || 'Delete failed');\n      }\n    } catch (error) {\n      setMessage('Network error');\n      console.error('Error deleting note:', error);\n    }\n  };\n  const handleCancel = () => {\n    setFormData({\n      title: '',\n      content: ''\n    });\n    setShowForm(false);\n    setEditingNote(null);\n    setMessage('');\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading\",\n      children: \"Loading notes...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"notes-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"notes-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"My Notes\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"add-note-btn\",\n        onClick: () => setShowForm(true),\n        children: \"Add New Note\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 7\n    }, this), message && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `message ${message.includes('successfully') ? 'success' : 'error'}`,\n      children: message\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 9\n    }, this), showForm && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"note-form-container\",\n      children: /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"note-form\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: editingNote ? 'Edit Note' : 'Create New Note'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          placeholder: \"Note title\",\n          value: formData.title,\n          onChange: e => setFormData({\n            ...formData,\n            title: e.target.value\n          }),\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n          placeholder: \"Note content\",\n          value: formData.content,\n          onChange: e => setFormData({\n            ...formData,\n            content: e.target.value\n          }),\n          rows: \"6\",\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-buttons\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"save-btn\",\n            children: editingNote ? 'Update Note' : 'Save Note'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: handleCancel,\n            className: \"cancel-btn\",\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"notes-list\",\n      children: notes.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"no-notes\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"No notes yet. Create your first note!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 11\n      }, this) : notes.map(note => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"note-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"note-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: note.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"note-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleEdit(note),\n              className: \"edit-btn\",\n              children: \"Edit\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleDelete(note.id),\n              className: \"delete-btn\",\n              children: \"Delete\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"note-content\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            children: note.content\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"note-timestamp\",\n          children: new Date(note.timestamp).toLocaleString()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 15\n        }, this)]\n      }, note.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 128,\n    columnNumber: 5\n  }, this);\n};\n_s(Notes, \"SHOBEneVtvUMMjvjhpE0BItsOzc=\");\n_c = Notes;\nexport default Notes;\nvar _c;\n$RefreshReg$(_c, \"Notes\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "Notes", "_s", "notes", "setNotes", "loading", "setLoading", "showForm", "setShowForm", "editingNote", "setEditingNote", "formData", "setFormData", "title", "content", "message", "setMessage", "fetchNotes", "token", "localStorage", "getItem", "response", "fetch", "headers", "ok", "data", "json", "error", "console", "handleSubmit", "e", "preventDefault", "trim", "url", "id", "method", "body", "JSON", "stringify", "handleEdit", "note", "handleDelete", "noteId", "window", "confirm", "handleCancel", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "includes", "onSubmit", "type", "placeholder", "value", "onChange", "target", "required", "rows", "length", "map", "Date", "timestamp", "toLocaleString", "_c", "$RefreshReg$"], "sources": ["D:/Projects/Notelizer/frontend/src/pages/Notes.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport './Notes.css';\n\nconst Notes = () => {\n  const [notes, setNotes] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [showForm, setShowForm] = useState(false);\n  const [editingNote, setEditingNote] = useState(null);\n  const [formData, setFormData] = useState({ title: '', content: '' });\n  const [message, setMessage] = useState('');\n\n  useEffect(() => {\n    fetchNotes();\n  }, []);\n\n  const fetchNotes = async () => {\n    try {\n      const token = localStorage.getItem('token');\n      const response = await fetch('http://localhost:5001/notes', {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        setNotes(data);\n      } else {\n        setMessage('Failed to fetch notes');\n      }\n    } catch (error) {\n      setMessage('Network error');\n      console.error('Error fetching notes:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setMessage('');\n\n    if (!formData.title.trim() || !formData.content.trim()) {\n      setMessage('Title and content are required');\n      return;\n    }\n\n    try {\n      const token = localStorage.getItem('token');\n      const url = editingNote \n        ? `http://localhost:5000/notes/${editingNote.id}`\n        : 'http://localhost:5000/notes';\n      \n      const method = editingNote ? 'PUT' : 'POST';\n\n      const response = await fetch(url, {\n        method,\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${token}`\n        },\n        body: JSON.stringify(formData)\n      });\n\n      const data = await response.json();\n\n      if (response.ok) {\n        setMessage(editingNote ? 'Note updated successfully!' : 'Note created successfully!');\n        setFormData({ title: '', content: '' });\n        setShowForm(false);\n        setEditingNote(null);\n        fetchNotes(); // Refresh the notes list\n      } else {\n        setMessage(data.message || 'Operation failed');\n      }\n    } catch (error) {\n      setMessage('Network error');\n      console.error('Error saving note:', error);\n    }\n  };\n\n  const handleEdit = (note) => {\n    setEditingNote(note);\n    setFormData({ title: note.title, content: note.content });\n    setShowForm(true);\n  };\n\n  const handleDelete = async (noteId) => {\n    if (!window.confirm('Are you sure you want to delete this note?')) {\n      return;\n    }\n\n    try {\n      const token = localStorage.getItem('token');\n      const response = await fetch(`http://localhost:5000/notes/${noteId}`, {\n        method: 'DELETE',\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      const data = await response.json();\n\n      if (response.ok) {\n        setMessage('Note deleted successfully!');\n        fetchNotes(); // Refresh the notes list\n      } else {\n        setMessage(data.message || 'Delete failed');\n      }\n    } catch (error) {\n      setMessage('Network error');\n      console.error('Error deleting note:', error);\n    }\n  };\n\n  const handleCancel = () => {\n    setFormData({ title: '', content: '' });\n    setShowForm(false);\n    setEditingNote(null);\n    setMessage('');\n  };\n\n  if (loading) {\n    return <div className=\"loading\">Loading notes...</div>;\n  }\n\n  return (\n    <div className=\"notes-container\">\n      <div className=\"notes-header\">\n        <h2>My Notes</h2>\n        <button \n          className=\"add-note-btn\"\n          onClick={() => setShowForm(true)}\n        >\n          Add New Note\n        </button>\n      </div>\n\n      {message && (\n        <div className={`message ${message.includes('successfully') ? 'success' : 'error'}`}>\n          {message}\n        </div>\n      )}\n\n      {showForm && (\n        <div className=\"note-form-container\">\n          <form onSubmit={handleSubmit} className=\"note-form\">\n            <h3>{editingNote ? 'Edit Note' : 'Create New Note'}</h3>\n            <input\n              type=\"text\"\n              placeholder=\"Note title\"\n              value={formData.title}\n              onChange={(e) => setFormData({ ...formData, title: e.target.value })}\n              required\n            />\n            <textarea\n              placeholder=\"Note content\"\n              value={formData.content}\n              onChange={(e) => setFormData({ ...formData, content: e.target.value })}\n              rows=\"6\"\n              required\n            />\n            <div className=\"form-buttons\">\n              <button type=\"submit\" className=\"save-btn\">\n                {editingNote ? 'Update Note' : 'Save Note'}\n              </button>\n              <button type=\"button\" onClick={handleCancel} className=\"cancel-btn\">\n                Cancel\n              </button>\n            </div>\n          </form>\n        </div>\n      )}\n\n      <div className=\"notes-list\">\n        {notes.length === 0 ? (\n          <div className=\"no-notes\">\n            <p>No notes yet. Create your first note!</p>\n          </div>\n        ) : (\n          notes.map((note) => (\n            <div key={note.id} className=\"note-card\">\n              <div className=\"note-header\">\n                <h3>{note.title}</h3>\n                <div className=\"note-actions\">\n                  <button onClick={() => handleEdit(note)} className=\"edit-btn\">\n                    Edit\n                  </button>\n                  <button onClick={() => handleDelete(note.id)} className=\"delete-btn\">\n                    Delete\n                  </button>\n                </div>\n              </div>\n              <div className=\"note-content\">\n                <p>{note.content}</p>\n              </div>\n              <div className=\"note-timestamp\">\n                {new Date(note.timestamp).toLocaleString()}\n              </div>\n            </div>\n          ))\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default Notes;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErB,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGP,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACQ,OAAO,EAAEC,UAAU,CAAC,GAAGT,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACU,QAAQ,EAAEC,WAAW,CAAC,GAAGX,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACY,WAAW,EAAEC,cAAc,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACc,QAAQ,EAAEC,WAAW,CAAC,GAAGf,QAAQ,CAAC;IAAEgB,KAAK,EAAE,EAAE;IAAEC,OAAO,EAAE;EAAG,CAAC,CAAC;EACpE,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAE1CC,SAAS,CAAC,MAAM;IACdmB,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,6BAA6B,EAAE;QAC1DC,OAAO,EAAE;UACP,eAAe,EAAE,UAAUL,KAAK;QAClC;MACF,CAAC,CAAC;MAEF,IAAIG,QAAQ,CAACG,EAAE,EAAE;QACf,MAAMC,IAAI,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;QAClCtB,QAAQ,CAACqB,IAAI,CAAC;MAChB,CAAC,MAAM;QACLT,UAAU,CAAC,uBAAuB,CAAC;MACrC;IACF,CAAC,CAAC,OAAOW,KAAK,EAAE;MACdX,UAAU,CAAC,eAAe,CAAC;MAC3BY,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C,CAAC,SAAS;MACRrB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMuB,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBf,UAAU,CAAC,EAAE,CAAC;IAEd,IAAI,CAACL,QAAQ,CAACE,KAAK,CAACmB,IAAI,CAAC,CAAC,IAAI,CAACrB,QAAQ,CAACG,OAAO,CAACkB,IAAI,CAAC,CAAC,EAAE;MACtDhB,UAAU,CAAC,gCAAgC,CAAC;MAC5C;IACF;IAEA,IAAI;MACF,MAAME,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMa,GAAG,GAAGxB,WAAW,GACnB,+BAA+BA,WAAW,CAACyB,EAAE,EAAE,GAC/C,6BAA6B;MAEjC,MAAMC,MAAM,GAAG1B,WAAW,GAAG,KAAK,GAAG,MAAM;MAE3C,MAAMY,QAAQ,GAAG,MAAMC,KAAK,CAACW,GAAG,EAAE;QAChCE,MAAM;QACNZ,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAE,UAAUL,KAAK;QAClC,CAAC;QACDkB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC3B,QAAQ;MAC/B,CAAC,CAAC;MAEF,MAAMc,IAAI,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;MAElC,IAAIL,QAAQ,CAACG,EAAE,EAAE;QACfR,UAAU,CAACP,WAAW,GAAG,4BAA4B,GAAG,4BAA4B,CAAC;QACrFG,WAAW,CAAC;UAAEC,KAAK,EAAE,EAAE;UAAEC,OAAO,EAAE;QAAG,CAAC,CAAC;QACvCN,WAAW,CAAC,KAAK,CAAC;QAClBE,cAAc,CAAC,IAAI,CAAC;QACpBO,UAAU,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,MAAM;QACLD,UAAU,CAACS,IAAI,CAACV,OAAO,IAAI,kBAAkB,CAAC;MAChD;IACF,CAAC,CAAC,OAAOY,KAAK,EAAE;MACdX,UAAU,CAAC,eAAe,CAAC;MAC3BY,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;IAC5C;EACF,CAAC;EAED,MAAMY,UAAU,GAAIC,IAAI,IAAK;IAC3B9B,cAAc,CAAC8B,IAAI,CAAC;IACpB5B,WAAW,CAAC;MAAEC,KAAK,EAAE2B,IAAI,CAAC3B,KAAK;MAAEC,OAAO,EAAE0B,IAAI,CAAC1B;IAAQ,CAAC,CAAC;IACzDN,WAAW,CAAC,IAAI,CAAC;EACnB,CAAC;EAED,MAAMiC,YAAY,GAAG,MAAOC,MAAM,IAAK;IACrC,IAAI,CAACC,MAAM,CAACC,OAAO,CAAC,4CAA4C,CAAC,EAAE;MACjE;IACF;IAEA,IAAI;MACF,MAAM1B,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,+BAA+BoB,MAAM,EAAE,EAAE;QACpEP,MAAM,EAAE,QAAQ;QAChBZ,OAAO,EAAE;UACP,eAAe,EAAE,UAAUL,KAAK;QAClC;MACF,CAAC,CAAC;MAEF,MAAMO,IAAI,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;MAElC,IAAIL,QAAQ,CAACG,EAAE,EAAE;QACfR,UAAU,CAAC,4BAA4B,CAAC;QACxCC,UAAU,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,MAAM;QACLD,UAAU,CAACS,IAAI,CAACV,OAAO,IAAI,eAAe,CAAC;MAC7C;IACF,CAAC,CAAC,OAAOY,KAAK,EAAE;MACdX,UAAU,CAAC,eAAe,CAAC;MAC3BY,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC9C;EACF,CAAC;EAED,MAAMkB,YAAY,GAAGA,CAAA,KAAM;IACzBjC,WAAW,CAAC;MAAEC,KAAK,EAAE,EAAE;MAAEC,OAAO,EAAE;IAAG,CAAC,CAAC;IACvCN,WAAW,CAAC,KAAK,CAAC;IAClBE,cAAc,CAAC,IAAI,CAAC;IACpBM,UAAU,CAAC,EAAE,CAAC;EAChB,CAAC;EAED,IAAIX,OAAO,EAAE;IACX,oBAAOL,OAAA;MAAK8C,SAAS,EAAC,SAAS;MAAAC,QAAA,EAAC;IAAgB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EACxD;EAEA,oBACEnD,OAAA;IAAK8C,SAAS,EAAC,iBAAiB;IAAAC,QAAA,gBAC9B/C,OAAA;MAAK8C,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3B/C,OAAA;QAAA+C,QAAA,EAAI;MAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACjBnD,OAAA;QACE8C,SAAS,EAAC,cAAc;QACxBM,OAAO,EAAEA,CAAA,KAAM5C,WAAW,CAAC,IAAI,CAAE;QAAAuC,QAAA,EAClC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAELpC,OAAO,iBACNf,OAAA;MAAK8C,SAAS,EAAE,WAAW/B,OAAO,CAACsC,QAAQ,CAAC,cAAc,CAAC,GAAG,SAAS,GAAG,OAAO,EAAG;MAAAN,QAAA,EACjFhC;IAAO;MAAAiC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACN,EAEA5C,QAAQ,iBACPP,OAAA;MAAK8C,SAAS,EAAC,qBAAqB;MAAAC,QAAA,eAClC/C,OAAA;QAAMsD,QAAQ,EAAEzB,YAAa;QAACiB,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACjD/C,OAAA;UAAA+C,QAAA,EAAKtC,WAAW,GAAG,WAAW,GAAG;QAAiB;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACxDnD,OAAA;UACEuD,IAAI,EAAC,MAAM;UACXC,WAAW,EAAC,YAAY;UACxBC,KAAK,EAAE9C,QAAQ,CAACE,KAAM;UACtB6C,QAAQ,EAAG5B,CAAC,IAAKlB,WAAW,CAAC;YAAE,GAAGD,QAAQ;YAAEE,KAAK,EAAEiB,CAAC,CAAC6B,MAAM,CAACF;UAAM,CAAC,CAAE;UACrEG,QAAQ;QAAA;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACFnD,OAAA;UACEwD,WAAW,EAAC,cAAc;UAC1BC,KAAK,EAAE9C,QAAQ,CAACG,OAAQ;UACxB4C,QAAQ,EAAG5B,CAAC,IAAKlB,WAAW,CAAC;YAAE,GAAGD,QAAQ;YAAEG,OAAO,EAAEgB,CAAC,CAAC6B,MAAM,CAACF;UAAM,CAAC,CAAE;UACvEI,IAAI,EAAC,GAAG;UACRD,QAAQ;QAAA;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACFnD,OAAA;UAAK8C,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B/C,OAAA;YAAQuD,IAAI,EAAC,QAAQ;YAACT,SAAS,EAAC,UAAU;YAAAC,QAAA,EACvCtC,WAAW,GAAG,aAAa,GAAG;UAAW;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,eACTnD,OAAA;YAAQuD,IAAI,EAAC,QAAQ;YAACH,OAAO,EAAEP,YAAa;YAACC,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAEpE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CACN,eAEDnD,OAAA;MAAK8C,SAAS,EAAC,YAAY;MAAAC,QAAA,EACxB5C,KAAK,CAAC2D,MAAM,KAAK,CAAC,gBACjB9D,OAAA;QAAK8C,SAAS,EAAC,UAAU;QAAAC,QAAA,eACvB/C,OAAA;UAAA+C,QAAA,EAAG;QAAqC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC,CAAC,GAENhD,KAAK,CAAC4D,GAAG,CAAEvB,IAAI,iBACbxC,OAAA;QAAmB8C,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACtC/C,OAAA;UAAK8C,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B/C,OAAA;YAAA+C,QAAA,EAAKP,IAAI,CAAC3B;UAAK;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACrBnD,OAAA;YAAK8C,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3B/C,OAAA;cAAQoD,OAAO,EAAEA,CAAA,KAAMb,UAAU,CAACC,IAAI,CAAE;cAACM,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAE9D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTnD,OAAA;cAAQoD,OAAO,EAAEA,CAAA,KAAMX,YAAY,CAACD,IAAI,CAACN,EAAE,CAAE;cAACY,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAErE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNnD,OAAA;UAAK8C,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3B/C,OAAA;YAAA+C,QAAA,EAAIP,IAAI,CAAC1B;UAAO;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC,eACNnD,OAAA;UAAK8C,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAC5B,IAAIiB,IAAI,CAACxB,IAAI,CAACyB,SAAS,CAAC,CAACC,cAAc,CAAC;QAAC;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC;MAAA,GAjBEX,IAAI,CAACN,EAAE;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAkBZ,CACN;IACF;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjD,EAAA,CA1MID,KAAK;AAAAkE,EAAA,GAALlE,KAAK;AA4MX,eAAeA,KAAK;AAAC,IAAAkE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}